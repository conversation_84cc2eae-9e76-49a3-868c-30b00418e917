server {
    listen 80;
    server_name test.bedribahru.com;

    location /.well-known/acme-challenge/ {
       root /var/www/certbot;
    }

    location / {
            return 301 https://$host$request_uri;
    }
}
server {
    listen 443 ssl;
    server_name test.bedribahru.com;

    ssl_certificate /etc/letsencrypt/live/test.bedribahru.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/test.bedribahru.com/privkey.pem;

    location / {
        proxy_pass http://localhost:3000; #for demo purposes
    }
}
