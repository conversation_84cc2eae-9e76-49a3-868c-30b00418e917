{"info": {"_postman_id": "********-2706ef32-ac86-4ffc-a7c8-dc57eab49b2b", "name": "Read-Sea-API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "<PERSON><PERSON>", "item": [{"name": "<PERSON><PERSON>", "id": "********-1f83bf41-65b1-4189-b310-76acbaea0930", "protocolProfileBehavior": {"disableBodyPruning": true, "disabledSystemHeaders": {}}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"#Include29\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/auth/login", "host": ["{{api_url}}"], "path": ["auth", "login"]}}, "response": []}, {"name": "Register", "id": "********-86e4e105-c513-4250-825b-5c039d95fc5a", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"#Include29\",\r\n    \"first_name\": \"<PERSON><PERSON>\",\r\n    \"last_name\": \"<PERSON><PERSON>\",\r\n    \"phone_number\": \"+251962886951\",\r\n    \"role_id\": \"3d2249d2-3400-4a37-b1be-e05f016c2b33\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/auth/register", "host": ["{{api_url}}"], "path": ["auth", "register"]}}, "response": []}, {"name": "Auth Check", "id": "********-24749c88-ef8b-492c-871f-174361e065db", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/auth", "host": ["{{api_url}}"], "path": ["auth"]}}, "response": []}, {"name": "Change Password", "id": "********-0fdcfc79-af0c-4f2b-8c02-47ae0675b647", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"previous_password\": \"1q2w3e4r5t6y7u8i9o0p\",\r\n    \"new_password\": \"1234Qwer!!\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/auth/change_password", "host": ["{{api_url}}"], "path": ["auth", "change_password"]}}, "response": []}, {"name": "Forgot Password", "id": "********-2544aba3-8c16-48d3-99d6-558644ca4a98", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"<EMAIL>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/auth/forgot_password", "host": ["{{api_url}}"], "path": ["auth", "forgot_password"]}}, "response": []}], "id": "********-d2b1c690-8c21-4c29-ba25-8853c4b5dd83"}, {"name": "User", "item": [{"name": "Access Rules", "item": [{"name": "Get All", "id": "********-4a8f74dc-5c01-4bee-b1e4-d2c6651a7d1b", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/access_rules?query=offset=0%26limit=500", "host": ["{{api_url}}"], "path": ["access_rules"], "query": [{"key": "query", "value": "offset=0%26limit=500"}]}}, "response": []}, {"name": "Get One", "id": "********-d1566549-0015-40c4-8393-f79574fa111f", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/access_rules/get", "host": ["{{api_url}}"], "path": ["access_rules", "get"], "query": [{"key": "query", "value": "search%5Bkeys%5D%5B0%5D=name%26search%5Bvalue%5D=bEDRId", "disabled": true}]}}, "response": []}, {"name": "Get By ID", "id": "********-5f4e16a3-d2b2-47a5-afef-02304cc3b08a", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/access_rules/akshdkjhaskjdhaksdhkjdhkasd", "host": ["{{api_url}}"], "path": ["access_rules", "akshdkjhaskjdhaksdhkjdhkasd"], "query": [{"key": "query", "value": "include%5B0%5D%5Bmodel%5D=interest&include%5B1%5D%5Bmodel%5D=file&include%5B1%5D%5Balias%5D=profile", "disabled": true}]}}, "response": []}, {"name": "Create", "id": "********-e71fdac3-513f-4aa2-aded-1cc0e5027c9f", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "{{api_url}}/access_rules", "host": ["{{api_url}}"], "path": ["access_rules"]}}, "response": []}, {"name": "Delete", "id": "********-6d46a82f-c009-4849-a8dd-060ca7458fb7", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": \"asjkhdkjash<PERSON>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/access_rules", "host": ["{{api_url}}"], "path": ["access_rules"]}}, "response": []}, {"name": "Rest<PERSON>", "id": "********-0fc74fa5-a4ef-4ae3-8c26-a868116b3b50", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": \"asjkhdkjash<PERSON>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/access_rules/restore", "host": ["{{api_url}}"], "path": ["access_rules", "restore"]}}, "response": []}], "id": "********-517bc45c-2a65-400f-a845-f57c09b172f1"}, {"name": "Action Logs", "item": [{"name": "Get All", "id": "********-d398f303-1ac2-4df3-b83a-0ca4acbf559f", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/action_logs", "host": ["{{api_url}}"], "path": ["action_logs"], "query": [{"key": "query", "value": "offset=0%26limit=5%26include%5B0%5D%5Bmodel%5D=interest", "disabled": true}]}}, "response": []}, {"name": "Get One", "id": "********-8319de3a-001d-47ce-9515-1b7d18c1cba6", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/action_logs/get", "host": ["{{api_url}}"], "path": ["action_logs", "get"], "query": [{"key": "query", "value": "search%5Bkeys%5D%5B0%5D=name%26search%5Bvalue%5D=bEDRId", "disabled": true}]}}, "response": []}, {"name": "Get By ID", "id": "********-b7bc4419-fc8c-4baa-a1d9-8e1e25715e4a", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/action_logs/akshdkjhaskjdhaksdhkjdhkasd", "host": ["{{api_url}}"], "path": ["action_logs", "akshdkjhaskjdhaksdhkjdhkasd"], "query": [{"key": "query", "value": "include%5B0%5D%5Bmodel%5D=interest&include%5B1%5D%5Bmodel%5D=file&include%5B1%5D%5Balias%5D=profile", "disabled": true}]}}, "response": []}, {"name": "Create", "id": "********-2c7cc254-b670-4971-9cbd-a32e6a60c9bc", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"action\": \"failure\",\r\n    \"object\": \"user\",\r\n    \"prev_data\": {},\r\n    \"new_data\": {}\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/action_logs", "host": ["{{api_url}}"], "path": ["action_logs"]}}, "response": []}, {"name": "Delete", "id": "********-719f324d-25cd-4ab6-9961-8353bd618868", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": \"asjkhdkjash<PERSON>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/action_logs", "host": ["{{api_url}}"], "path": ["action_logs"]}}, "response": []}, {"name": "Rest<PERSON>", "id": "********-e123efd6-d0ed-4cee-93ab-74a7e58a5d64", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": \"asjkhdkjash<PERSON>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/action_logs/restore", "host": ["{{api_url}}"], "path": ["action_logs", "restore"]}}, "response": []}], "id": "********-fc9bd022-5dda-4847-a8ae-7ad5e72aab51"}, {"name": "Role", "item": [{"name": "Get All", "id": "********-354510da-c4aa-4e53-9880-0b1ca7869158", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/roles", "host": ["{{api_url}}"], "path": ["roles"], "query": [{"key": "query", "value": "offset=0%26limit=5%26include%5B0%5D%5Bmodel%5D=user", "disabled": true}]}}, "response": []}, {"name": "Get All Copy", "id": "********-d8a3e23a-a21b-4d9b-8df9-63a0310f170a", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/roles/selected-roles", "host": ["{{api_url}}"], "path": ["roles", "selected-roles"]}}, "response": []}, {"name": "Get One", "id": "********-408c160b-2682-4016-9175-3bf0877f8416", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/roles/get", "host": ["{{api_url}}"], "path": ["roles", "get"], "query": [{"key": "query", "value": "search%5Bkeys%5D%5B0%5D=name%26search%5Bvalue%5D=bEDRId", "disabled": true}]}}, "response": []}, {"name": "Get By ID", "id": "********-018e8002-935d-4562-b4a9-1312cf3eb766", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/roles/e48f7ed1-db16-4511-be6a-87ef32aab99d", "host": ["{{api_url}}"], "path": ["roles", "e48f7ed1-db16-4511-be6a-87ef32aab99d"], "query": [{"key": "query", "value": "include%5B0%5D%5Bmodel%5D=interest&include%5B1%5D%5Bmodel%5D=file&include%5B1%5D%5Balias%5D=profile", "disabled": true}]}}, "response": []}, {"name": "Create", "id": "********-e6295b18-b41b-4188-b3bc-8f596257d782", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"Super Admin\",\r\n    \"description\": \"The absolute powerful admin role.\",\r\n    \"access_rules\": [\r\n        \"read_file\",\r\n        \"write_file\",\r\n        \"read_access_rule\",\r\n        \"read_role\",\r\n        \"write_role\",\r\n        \"delete_role\",\r\n        \"read_user\",\r\n        \"write_user\"\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/roles", "host": ["{{api_url}}"], "path": ["roles"]}}, "response": []}, {"name": "Update", "id": "********-d4dae33a-2bc3-4c1b-834b-1bda4fdb7078", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"access_rules\": [\r\n        \"read_file\",\r\n        \"write_file\",\r\n        \"read_access_rule\",\r\n        \"read_role\",\r\n        \"write_role\",\r\n        \"delete_role\",\r\n        \"read_user\",\r\n        \"write_user\",\r\n        \"delete_listing_favorite\",\r\n        \"write_listing_favorite\",\r\n        \"read_listing_favorite\",\r\n        \"read_payout\",\r\n        \"write_payout\",\r\n        \"read_refund\",\r\n        \"write_refund\",\r\n        \"read_config\"\r\n    ],\r\n    \"id\": \"3d2249d2-3400-4a37-b1be-e05f016c2b33\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/roles", "host": ["{{api_url}}"], "path": ["roles"]}}, "response": []}, {"name": "Delete", "id": "********-84e4cff5-945e-4a7a-b2c7-2c5bcc8aa9cb", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": \"asjkhdkjash<PERSON>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/roles", "host": ["{{api_url}}"], "path": ["roles"]}}, "response": []}, {"name": "Rest<PERSON>", "id": "********-b1c08aa7-acd4-4ac8-9376-0fde7ac3d1ea", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": \"asjkhdkjash<PERSON>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/roles/restore", "host": ["{{api_url}}"], "path": ["roles", "restore"]}}, "response": []}], "id": "********-4ec156bd-e746-484b-89a8-6f0caaa71458"}, {"name": "File", "item": [{"name": "Get All", "id": "********-f1517d7d-5e3a-468b-8dc6-3c448d9063f1", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/files", "host": ["{{api_url}}"], "path": ["files"], "query": [{"key": "query", "value": "", "disabled": true}]}}, "response": []}, {"name": "Get One", "id": "********-9d0881c1-9620-4cba-b00f-7644785c85db", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/roles/get", "host": ["{{api_url}}"], "path": ["roles", "get"], "query": [{"key": "query", "value": "search%5Bkeys%5D%5B0%5D=name%26search%5Bvalue%5D=bEDRId", "disabled": true}]}}, "response": []}, {"name": "Get By ID", "id": "********-0d742576-219d-4f97-94e8-9b157cd614bc", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/roles/akshdkjhaskjdhaksdhkjdhkasd", "host": ["{{api_url}}"], "path": ["roles", "akshdkjhaskjdhaksdhkjdhkasd"], "query": [{"key": "query", "value": "include%5B0%5D%5Bmodel%5D=interest&include%5B1%5D%5Bmodel%5D=file&include%5B1%5D%5Balias%5D=profile", "disabled": true}]}}, "response": []}, {"name": "Create", "id": "********-8c63076d-b2a8-4640-9383-47cbaa88bc73", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": "/C:/Users/<USER>/Downloads/Telegram Desktop/1742710897239.gif"}]}, "url": {"raw": "{{api_url}}/files/single", "host": ["{{api_url}}"], "path": ["files", "single"]}}, "response": []}, {"name": "Bulk Create", "id": "********-db6dc9cb-42ad-4e8b-9755-99486e1ceea0", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "files", "type": "file", "src": ["/C:/Users/<USER>/Downloads/Untitled design.png", "/C:/Users/<USER>/Downloads/Screenshot (5).png", "/C:/Users/<USER>/Downloads/Boingo API. - Affiliate.jpg", "/C:/Users/<USER>/Downloads/image-20250103-071104.png"]}]}, "url": {"raw": "{{api_url}}/files/multiple", "host": ["{{api_url}}"], "path": ["files", "multiple"]}}, "response": []}, {"name": "Update", "id": "********-f4d2f985-f1b2-45f1-806d-e89d4b120061", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": \"5fb3985b-8954-4396-9085-bc6001cd353a\",\r\n    \"access_rules\": [\r\n        \"read_access_rule\",\r\n        \"write_access_rule\",\r\n        \"read_role\",\r\n        \"write_role\",\r\n        \"read_user\",\r\n        \"write_user\",\r\n        \"read_destination\",\r\n        \"write_destination\",\r\n        \"read_vehicle\",\r\n        \"write_vehicle\",\r\n        \"read_association\",\r\n        \"write_association\",\r\n        \"read_level\",\r\n        \"write_level\",\r\n        \"read_plate_type\",\r\n        \"write_plate_type\",\r\n        \"read_seat_number\",\r\n        \"write_seat_number\",\r\n        \"read_station\",\r\n        \"write_station\",\r\n        \"read_trip\",\r\n        \"write_trip\",\r\n        \"read_route\",\r\n        \"write_route\",\r\n        \"read_config\",\r\n        \"write_config\",\r\n        \"read_action_log\",\r\n        \"write_action_log\",\r\n        \"sync_data\",\r\n        \"sync_trip\",\r\n        \"sync_vehicle\"\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/roles", "host": ["{{api_url}}"], "path": ["roles"]}}, "response": []}, {"name": "Delete", "id": "********-1102713c-dcf2-4eee-876a-ce08e3fea9ae", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": \"asjkhdkjash<PERSON>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/roles", "host": ["{{api_url}}"], "path": ["roles"]}}, "response": []}, {"name": "Rest<PERSON>", "id": "********-987df16d-1c52-40df-9790-7d51b590c38b", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": \"asjkhdkjash<PERSON>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/roles/restore", "host": ["{{api_url}}"], "path": ["roles", "restore"]}}, "response": []}], "id": "********-26e36007-63fb-4bb6-ba7c-e6f20d62a050"}, {"name": "User", "item": [{"name": "Get All", "id": "********-205308c9-3fd7-47b8-b0b4-03aa9cafd541", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/users?query=<EMAIL>%253As", "host": ["{{api_url}}"], "path": ["users"], "query": [{"key": "query", "value": "<EMAIL>%253As"}]}}, "response": []}, {"name": "Get One", "id": "********-64fda48b-7659-4995-8b12-0c6ccde12bf3", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/users/get?query=offset%3D0%253An%26limit%3D50%253An%26search%255Bkeys%255D%255B0%255D%3Demail%253As%26search%255Bvalue%255D%3Dnatnaelassefa27%2540gmail.com%253As", "host": ["{{api_url}}"], "path": ["users", "get"], "query": [{"key": "query", "value": "offset%3D0%253An%26limit%3D50%253An%26filter%255B0%255D%255Bkey%255D%3Dphone_number%253As%26filter%255B0%255D%255Boperator%255D%3D%253D%253As%26filter%255B0%255D%255Bvalue%255D%3D529841220801%253As", "disabled": true}, {"key": "query", "value": "offset%3D0%253An%26limit%3D50%253An%26search%255Bkeys%255D%255B0%255D%3Demail%253As%26search%255Bvalue%255D%3Dnatnaelassefa27%2540gmail.com%253As", "type": "text"}]}}, "response": []}, {"name": "Get By ID", "id": "********-d1d47fed-fefd-40a1-af0d-4d157f256393", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/users/42f4b746-a3f5-49a0-ac08-57cbe91cc898", "host": ["{{api_url}}"], "path": ["users", "42f4b746-a3f5-49a0-ac08-57cbe91cc898"], "query": [{"key": "query", "value": "offset%3D0%253An%26limit%3D1%253An%26include%255B0%255D%255Bmodel%255D%3Daffiliate%253As", "disabled": true}]}}, "response": []}, {"name": "Get Affiliate By User ID", "id": "********-5f5d32d3-16c0-4e5b-8b43-dc45772ed020", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/users/affiliate?query=offset%3D0%253An%26limit%3D50%253An%26include%255B0%255D%255Bmodel%255D%3Duser%253As", "host": ["{{api_url}}"], "path": ["users", "affiliate"], "query": [{"key": "query", "value": "offset%3D0%253An%26limit%3D50%253An%26include%255B0%255D%255Bmodel%255D%3Duser%253As"}]}}, "response": []}, {"name": "Create", "id": "********-a3e0d8c7-c1ec-4b2c-8490-5ee2a0be7cb7", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"1234Qwer!!\",\r\n    \"first_name\": \"Test\",\r\n    \"last_name\": \"<PERSON><PERSON>\",\r\n    \"phone_number\": \"529841361032\",\r\n    \"role_id\": \"6bbf4856-8366-45ed-839d-edadadb31b5a\",\r\n    \"status\": \"Active\",\r\n    \"type\": \"Broker\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/users", "host": ["{{api_url}}"], "path": ["users"]}}, "response": []}, {"name": "Change Password", "id": "********-f206b65e-dac3-43ba-b788-482db6aef580", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"user_id\": \"0ce838e8-98e8-4b82-b6a8-532853a119b3\",\r\n    \"password\": \"1234Qwer!\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/users/change_password", "host": ["{{api_url}}"], "path": ["users", "change_password"]}}, "response": []}, {"name": "Revoke User Token", "id": "********-18f2b899-fedd-4592-a8f9-51085370573a", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"user_id\": \"<EMAIL>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/users/revoke_token", "host": ["{{api_url}}"], "path": ["users", "revoke_token"]}}, "response": []}, {"name": "Update Assignation", "id": "********-316916bb-c3bf-447d-b91b-2b744f5b5a92", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"user_id\": \"<EMAIL>\",\r\n    \"region_id\": \"\",\r\n    \"zone_id\": \"\",\r\n    \"city_id\": \"\",\r\n    \"station_id\": \"\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/users/update_assignation", "host": ["{{api_url}}"], "path": ["users", "update_assignation"]}}, "response": []}, {"name": "Update", "id": "********-275d3265-4d67-45b4-8051-07cf94f59a32", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": \"1d470e0e-30da-4dcb-ad85-7cc41e16b1e5\",\r\n   \"password\": \"#Include29\",\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/users", "host": ["{{api_url}}"], "path": ["users"]}}, "response": []}, {"name": "Onboarding Seen", "id": "********-ec5d9c54-f53c-43fd-a8fd-11aeb3618456", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": \"1d470e0e-30da-4dcb-ad85-7cc41e16b1e5\",\r\n   \"password\": \"#Include29\",\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/users", "host": ["{{api_url}}"], "path": ["users"]}}, "response": []}, {"name": "Subscribe", "id": "********-017ad4ea-1049-401e-9c3b-ae850e8056aa", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": \"48eef8c5-7690-48ca-ab44-89e82536e2e3\",\r\n    \"has_subscribed\": false\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/users/subscribe", "host": ["{{api_url}}"], "path": ["users", "subscribe"]}}, "response": []}, {"name": "Filter", "id": "********-6657e434-79bf-4660-915c-a7610af80d7e", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"from\": \"01 December 2023\",\r\n    \"to\": \"15 March 2025\",\r\n    \"role_id\": \"6bbf4856-8366-45ed-839d-edadadb31b5a\",\r\n    \"status\": \"Pending\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/users/filter", "host": ["{{api_url}}"], "path": ["users", "filter"]}}, "response": []}, {"name": "Delete", "id": "********-7818a481-b892-400a-9b33-b1a4f0f7db6a", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": \"3b0135c6-0456-4ca3-99d7-1d5627bfed1f\",\r\n    \"force\": true\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/users", "host": ["{{api_url}}"], "path": ["users"]}}, "response": []}, {"name": "Rest<PERSON>", "id": "********-9bf68d26-db4a-49d0-9895-ea865d57e162", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": \"asjkhdkjash<PERSON>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/users/restore", "host": ["{{api_url}}"], "path": ["users", "restore"]}}, "response": []}], "id": "********-02c85732-d416-4d10-b6e0-2853a7906cc1"}, {"name": "User Profile", "item": [{"name": "Get All", "id": "********-1e52023d-2159-43f1-8462-3a766815d653", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/user_profiles?query=offset%3D0%253An%26limit%3D10%253An%26include%255B0%255D%255Bmodel%255D%3Duser%253As%26include%255B1%255D%255Bmodel%255D%3Dfile%253As", "host": ["{{api_url}}"], "path": ["user_profiles"], "query": [{"key": "query", "value": "offset%3D0%253An%26limit%3D10%253An%26include%255B0%255D%255Bmodel%255D%3Duser%253As%26include%255B1%255D%255Bmodel%255D%3Dfile%253As"}]}}, "response": []}, {"name": "Get One", "id": "********-4cd6c689-0fe1-4e4a-ab5f-2c0cc395ec44", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/user_profiles/get?query=offset%3D0%253An%26limit%3D50%253An%26filter%255B0%255D%255Bkey%255D%3Dphone_number%253As%26filter%255B0%255D%255Boperator%255D%3D%253D%253As%26filter%255B0%255D%255Bvalue%255D%3D529841220801%253As", "host": ["{{api_url}}"], "path": ["user_profiles", "get"], "query": [{"key": "query", "value": "offset%3D0%253An%26limit%3D50%253An%26filter%255B0%255D%255Bkey%255D%3Dphone_number%253As%26filter%255B0%255D%255Boperator%255D%3D%253D%253As%26filter%255B0%255D%255Bvalue%255D%3D529841220801%253As"}]}}, "response": []}, {"name": "Get My Profile", "id": "********-eb4e766e-1859-4760-89ac-50b4d575634a", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/user_profiles/profile?query=offset%3D0%253An%26limit%3D10%253An%26include%255B0%255D%255Bmodel%255D%3Duser%253As%26include%255B1%255D%255Bmodel%255D%3Dfile%253As", "host": ["{{api_url}}"], "path": ["user_profiles", "profile"], "query": [{"key": "query", "value": "offset%3D0%253An%26limit%3D10%253An%26include%255B0%255D%255Bmodel%255D%3Duser%253As%26include%255B1%255D%255Bmodel%255D%3Dfile%253As"}]}}, "response": []}, {"name": "Get By ID", "id": "********-c0fa8cc3-8193-447d-9cb1-cb1f8fc11af7", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/user_profiles/abc023bd-6b27-4712-99a3-30685a47a58e", "host": ["{{api_url}}"], "path": ["user_profiles", "abc023bd-6b27-4712-99a3-30685a47a58e"], "query": [{"key": "query", "value": "offset%3D0%253An%26limit%3D1%253An%26include%255B0%255D%255Bmodel%255D%3Daffiliate%253As", "disabled": true}]}}, "response": []}, {"name": "Create", "id": "********-c13c9d83-cce7-4d33-b26d-a5e792f9fb5c", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"user_id\": \"b6e551f4-4cf8-4892-b586-0b787e78f7f7\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/user_profiles", "host": ["{{api_url}}"], "path": ["user_profiles"]}}, "response": []}, {"name": "Update", "id": "********-45759b0a-df9c-4de2-b477-436b4e38f12b", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": \"07f70945-5bc4-467f-a6b5-59475492e7c2\",\r\n    \"status\": \"Active\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/user_profiles", "host": ["{{api_url}}"], "path": ["user_profiles"]}}, "response": []}, {"name": "Delete", "id": "********-30320562-14d8-4b50-b231-5987602806d6", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": \"asjkhdkjash<PERSON>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/user_profiles", "host": ["{{api_url}}"], "path": ["user_profiles"]}}, "response": []}, {"name": "Rest<PERSON>", "id": "********-e4976f2b-4c2a-4b9c-a5c9-823603469564", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": \"asjkhdkjash<PERSON>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/users/restore", "host": ["{{api_url}}"], "path": ["users", "restore"]}}, "response": []}], "id": "********-793c8e81-a6ec-4ed5-a676-35d5c14c7529"}], "id": "********-2ac344e7-7296-496e-8475-caacbe62058a"}, {"name": "System", "item": [{"name": "Config", "item": [{"name": "Create", "id": "********-056563c4-677a-43a4-bf6a-1a647e570e14", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"key\": \"cancellation_reasons\",\r\n    \"object_type\": \"String\",\r\n    \"type\": \"string\",\r\n    \"value\": \"[\\\"Too expensive\\\", \\\"Not using the service enough\\\", \\\"Missing features\\\", \\\"Other reason\\\", \\\"Found alternative\\\", \\\"Technical issues\\\" ]\\n\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/configs", "host": ["{{api_url}}"], "path": ["configs"]}}, "response": []}, {"name": "Update", "id": "********-4a235f08-c74e-47e3-ae12-a16902f1fd65", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": \"\",\r\n    \"key\": \"Health Status\",\r\n    \"value\": \"Healthy\",\r\n    \"type\": \"String\",\r\n    \"object_type\": \"String\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/configs", "host": ["{{api_url}}"], "path": ["configs"]}}, "response": []}, {"name": "Get By Id", "id": "********-cf7860ce-67ab-4e87-b464-1d178f46fd52", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"key\": \"Health Status\",\r\n    \"value\": \"Healthy\",\r\n    \"type\": \"String\",\r\n    \"object_type\": \"String\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/configs", "host": ["{{api_url}}"], "path": ["configs"]}}, "response": []}, {"name": "Get All", "id": "********-c5ef8e74-1259-4346-8092-aea128423439", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"key\": \"Health Status\",\r\n    \"value\": \"Healthy\",\r\n    \"type\": \"String\",\r\n    \"object_type\": \"String\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/configs", "host": ["{{api_url}}"], "path": ["configs"]}}, "response": []}], "id": "********-b7221a00-29b4-40a9-a3e8-0ab733d5700e"}, {"name": "Notification", "item": [{"name": "Create", "id": "********-02485238-6f53-4ad4-ab4a-01058a01ce71", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"user_id\": \"3ef7a016-f8be-47d2-b8d5-8ad861eab87e\",\r\n    \"notification_title\": \"Subscription Started\",\r\n    \"notification_body\": \"Your subscription for 3 listings is being processed. You will be charged $95.97 per month.\",\r\n    \"notification_type\": \"Success\",\r\n    \"is_read\": false,\r\n    \"notification_date\": \"2025-02-06T16:50:50.358Z\",\r\n    \"notification_url\": \"http://boingo.ai/subscriptions\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/notification", "host": ["{{api_url}}"], "path": ["notification"]}}, "response": []}, {"name": "Update", "id": "********-7d66d238-c5fb-4ba2-9403-9189958a95de", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": \"66dfe9c6-ab79-4a2f-a105-1dd5c4137036\",\r\n    \"createdAt\": \"2025-02-01T13:56:49.171Z\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/notification", "host": ["{{api_url}}"], "path": ["notification"]}}, "response": []}, {"name": "Get By Id", "id": "********-471b6ceb-34a6-4cec-972f-4674d5f56bae", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"key\": \"Health Status\",\r\n    \"value\": \"Healthy\",\r\n    \"type\": \"String\",\r\n    \"object_type\": \"String\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/notification/71b885bd-7e54-4ada-b499-c0fb575f4ac1", "host": ["{{api_url}}"], "path": ["notification", "71b885bd-7e54-4ada-b499-c0fb575f4ac1"]}}, "response": []}, {"name": "Get All", "id": "********-1e7b0d33-b93d-4857-b9ef-321d01700607", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"key\": \"Health Status\",\r\n    \"value\": \"Healthy\",\r\n    \"type\": \"String\",\r\n    \"object_type\": \"String\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/notification", "host": ["{{api_url}}"], "path": ["notification"]}}, "response": []}, {"name": "Filter By Date", "id": "********-9320dd0e-70c5-4fc8-ac9c-cffc656a1894", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"is_read\": \"false\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/notification/filter?query=offset%3D0%253An%26limit%3D5%253An%26order%255B0%255D%255Bkey%255D%3DcreatedAt%253As%26order%255B0%255D%255Bvalue%255D%3DDESC%253As", "host": ["{{api_url}}"], "path": ["notification", "filter"], "query": [{"key": "query", "value": "offset%3D0%253An%26limit%3D5%253An%26order%255B0%255D%255Bkey%255D%3DcreatedAt%253As%26order%255B0%255D%255Bvalue%255D%3DDESC%253As"}]}}, "response": []}, {"name": "Get My Notification", "id": "********-e4809a3e-62d4-4cf1-93d3-b0349fe09f00", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"key\": \"Health Status\",\r\n    \"value\": \"Healthy\",\r\n    \"type\": \"String\",\r\n    \"object_type\": \"String\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/notification/my", "host": ["{{api_url}}"], "path": ["notification", "my"]}}, "response": []}], "id": "********-180c723b-0ead-47e5-9975-f3ebc35a297a"}], "id": "********-6ab45a79-2afc-4589-9aaa-0dc9ad1a083c"}, {"name": "Marketplace", "item": [{"name": "Promo Code", "item": [{"name": "Get All", "id": "********-7303f0ab-64fe-4e2e-b14c-ef22a92810ca", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/promo-codes", "host": ["{{api_url}}"], "path": ["promo-codes"], "query": [{"key": "query", "value": "offset=0%26limit=5%26include%5B0%5D%5Bmodel%5D=user", "disabled": true}]}}, "response": []}, {"name": "Get One", "id": "********-5568864a-258b-4e10-b29f-4fc6bf962cf4", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/promo-code/get", "host": ["{{api_url}}"], "path": ["promo-code", "get"], "query": [{"key": "query", "value": "search%5Bkeys%5D%5B0%5D=name%26search%5Bvalue%5D=bEDRId", "disabled": true}]}}, "response": []}, {"name": "Get By ID", "id": "********-3a871438-87dd-44d0-ab6e-32a3775fca36", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/promo-codes/ae2c3ae1-df76-4b00-95b1-aba48386bf34", "host": ["{{api_url}}"], "path": ["promo-codes", "ae2c3ae1-df76-4b00-95b1-aba48386bf34"], "query": [{"key": "query", "value": "include%5B0%5D%5Bmodel%5D=interest&include%5B1%5D%5Bmodel%5D=file&include%5B1%5D%5Balias%5D=profile", "disabled": true}]}}, "response": []}, {"name": "Get By Code", "id": "********-ec675cad-17ee-41d3-9456-e320e672c717", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/promo-codes/code/Best-Offer-35", "host": ["{{api_url}}"], "path": ["promo-codes", "code", "Best-Offer-35"], "query": [{"key": "query", "value": "include%5B0%5D%5Bmodel%5D=interest&include%5B1%5D%5Bmodel%5D=file&include%5B1%5D%5Balias%5D=profile", "disabled": true}]}}, "response": []}, {"name": "Calculate Amount", "id": "********-099d7fc1-13b1-4b59-931c-298c834b7c37", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/promo-codes/Best-Offer-35/calculate/900", "host": ["{{api_url}}"], "path": ["promo-codes", "Best-Offer-35", "calculate", "900"], "query": [{"key": "query", "value": "include%5B0%5D%5Bmodel%5D=interest&include%5B1%5D%5Bmodel%5D=file&include%5B1%5D%5Balias%5D=profile", "disabled": true}]}}, "response": []}, {"name": "Create", "id": "********-f495ce9e-c03c-4a1a-91e0-302c1282497d", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"code\": \"Best-Offer-35\",\r\n    \"discount_percentage\": 35\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/promo-codes", "host": ["{{api_url}}"], "path": ["promo-codes"]}}, "response": []}, {"name": "Update", "id": "********-e72af796-8995-4248-bddc-f7d32a2c794a", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": \"088ea6ea-fc2d-4883-a746-ffd11eb22242\",\r\n    \"status\": \"Inactive\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/promo-code", "host": ["{{api_url}}"], "path": ["promo-code"]}}, "response": []}, {"name": "Delete", "id": "********-d1eae970-132e-4f0c-ad67-19a41c8e1b80", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": \"16e79028-fd2c-41e5-aaf4-6a1867ecb3b6\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/promo-code", "host": ["{{api_url}}"], "path": ["promo-code"]}}, "response": []}, {"name": "Rest<PERSON>", "id": "********-e40931b9-b956-4ee4-a042-e10f76f98ce4", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": \"c92c4350-6420-4c05-810b-56fd125570b7\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/promo-code/restore", "host": ["{{api_url}}"], "path": ["promo-code", "restore"]}}, "response": []}], "id": "********-6f9dde8a-dde3-48d4-9cd0-2130236f02bb", "description": "Creating a referral link system and tracking clicks, sign-ins store user info and subscription information in every affiliate registry."}, {"name": "Address", "item": [{"name": "Get By Coordinates", "id": "********-4067ec44-08cc-4035-b24b-8b73a573c918", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "  {\r\n    \"lat\": \"0.6295586\",\r\n    \"lng\": \"-0.0738851\",\r\n    \"radius\": 1000000\r\n}\r\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/addresses/coordinates", "host": ["{{api_url}}"], "path": ["addresses", "coordinates"], "query": [{"key": "query", "value": "offset%3D0%253An%26limit%3D4%253An%26include%255B0%255D%255Bmodel%255D%3Dlisting%253As%26include%255B0%255D%255Binclude%255D%255B0%255D%255Bmodel%255D%3Dlisting-feature%253As%26include%255B0%255D%255Binclude%255D%255B0%255D%255Binclude%255D%255B0%255D%255Bmodel%255D%3Dfeature%253As%26include%255B0%255D%255Binclude%255D%255B1%255D%255Bmodel%255D%3Dcategory%253As%26include%255B0%255D%255Binclude%255D%255B2%255D%255Bmodel%255D%3Dlisting-type%253As%26include%255B0%255D%255Binclude%255D%255B2%255D%255Binclude%255D%255B0%255D%255Bmodel%255D%3Dtype%253As%26include%255B0%255D%255Binclude%255D%255B3%255D%255Bmodel%255D%3Dlisting-media%253As%26include%255B0%255D%255Binclude%255D%255B3%255D%255Binclude%255D%255B0%255D%255Bmodel%255D%3Dfile%253As\n", "disabled": true}]}}, "response": []}, {"name": "Get All", "id": "********-487f9a3d-ded9-42ca-bb97-1a119fb3e8a0", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/addresses", "host": ["{{api_url}}"], "path": ["addresses"], "query": [{"key": "query", "value": "offset=0%26limit=5%26include%5B0%5D%5Bmodel%5D=user", "disabled": true}]}}, "response": []}, {"name": "Get One", "id": "********-c274913c-257d-493c-b8ce-cc8e65a6e239", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/addresses/get", "host": ["{{api_url}}"], "path": ["addresses", "get"], "query": [{"key": "query", "value": "search%5Bkeys%5D%5B0%5D=name%26search%5Bvalue%5D=bEDRId", "disabled": true}]}}, "response": []}, {"name": "Get By ID", "id": "********-fd844254-ce07-429b-953e-d93437fa41af", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/addresses/akshdkjhaskjdhaksdhkjdhkasd", "host": ["{{api_url}}"], "path": ["addresses", "akshdkjhaskjdhaksdhkjdhkasd"], "query": [{"key": "query", "value": "include%5B0%5D%5Bmodel%5D=interest&include%5B1%5D%5Bmodel%5D=file&include%5B1%5D%5Balias%5D=profile", "disabled": true}]}}, "response": []}, {"name": "Create", "id": "********-68077226-337d-4b3a-9341-0ab68c13386f", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"user_id\": \"42f4b746-a3f5-49a0-ac08-57cbe91cc898\",\r\n    \"name\": \"Home\",\r\n    \"address_line1\": \"123 Admin St\",\r\n    \"address_line2\": null,\r\n    \"city\": \"Addis Abeba\",\r\n    \"state\": \"Koye Feche\",\r\n    \"postal_code\": \"12345\",\r\n    \"country\": \"Ethiopia\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/addresses", "host": ["{{api_url}}"], "path": ["addresses"]}}, "response": []}, {"name": "Update", "id": "********-40fdf249-062c-414d-bd4f-9bb7e50841b0", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": \"5fb3985b-8954-4396-9085-bc6001cd353a\",\r\n    \"access_rules\": [\r\n        \"read_access_rule\",\r\n        \"write_access_rule\",\r\n        \"read_role\",\r\n        \"write_role\",\r\n        \"read_user\",\r\n        \"write_user\",\r\n        \"read_destination\",\r\n        \"write_destination\",\r\n        \"read_vehicle\",\r\n        \"write_vehicle\",\r\n        \"read_association\",\r\n        \"write_association\",\r\n        \"read_level\",\r\n        \"write_level\",\r\n        \"read_plate_type\",\r\n        \"write_plate_type\",\r\n        \"read_seat_number\",\r\n        \"write_seat_number\",\r\n        \"read_station\",\r\n        \"write_station\",\r\n        \"read_trip\",\r\n        \"write_trip\",\r\n        \"read_route\",\r\n        \"write_route\",\r\n        \"read_config\",\r\n        \"write_config\",\r\n        \"read_action_log\",\r\n        \"write_action_log\",\r\n        \"sync_data\",\r\n        \"sync_trip\",\r\n        \"sync_vehicle\"\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/addresses", "host": ["{{api_url}}"], "path": ["addresses"]}}, "response": []}, {"name": "Delete", "id": "********-9097ff3b-8025-43a5-9d4c-27d9131fc90f", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": \"asjkhdkjash<PERSON>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/addresses", "host": ["{{api_url}}"], "path": ["addresses"]}}, "response": []}, {"name": "Rest<PERSON>", "id": "********-5a594a6a-4d7b-4f42-b859-d10f6d53ce23", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": \"asjkhdkjash<PERSON>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/addresses/restore", "host": ["{{api_url}}"], "path": ["addresses", "restore"]}}, "response": []}], "id": "********-a169c74c-1603-4382-abfd-a4196c3329bb"}, {"name": "App Setting", "item": [{"name": "Get All", "id": "********-2c598539-5966-4555-a72b-6b821a907d9d", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/app-settings", "host": ["{{api_url}}"], "path": ["app-settings"], "query": [{"key": "query", "value": "offset=0%26limit=5%26include%5B0%5D%5Bmodel%5D=user", "disabled": true}]}}, "response": []}, {"name": "Get One", "id": "********-22e2d489-99bb-43d0-9e68-2e77fda9fe2c", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/app-settings/get", "host": ["{{api_url}}"], "path": ["app-settings", "get"], "query": [{"key": "query", "value": "search%5Bkeys%5D%5B0%5D=name%26search%5Bvalue%5D=bEDRId", "disabled": true}]}}, "response": []}, {"name": "Get By ID", "id": "********-24b3f89b-0fd5-41fc-901c-9d7cb1c351ea", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/app-settings/akshdkjhaskjdhaksdhkjdhkasd", "host": ["{{api_url}}"], "path": ["app-settings", "akshdkjhaskjdhaksdhkjdhkasd"], "query": [{"key": "query", "value": "include%5B0%5D%5Bmodel%5D=interest&include%5B1%5D%5Bmodel%5D=file&include%5B1%5D%5Balias%5D=profile", "disabled": true}]}}, "response": []}, {"name": "Create", "id": "********-7d109fe8-2c6e-4350-ace4-d4ba709345fe", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"user_id\": \"edf50692-1d5f-41b0-90bd-be712243df87\",\r\n    \"language\": \"en\",\r\n    \"theme\": \"light\",\r\n    \"notifications_enabled\": true,\r\n    \"email_notifications\": true,\r\n    \"push_notifications\": true\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/app-settings", "host": ["{{api_url}}"], "path": ["app-settings"]}}, "response": []}, {"name": "Update", "id": "********-c8066a87-6372-4046-aee1-4d195c658a9d", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": \"5fb3985b-8954-4396-9085-bc6001cd353a\",\r\n    \"user_id\": \"edf50692-1d5f-41b0-90bd-be712243df87\",\r\n    \"language\": \"en\",\r\n    \"theme\": \"light\",\r\n    \"notifications_enabled\": true,\r\n    \"email_notifications\": true,\r\n    \"push_notifications\": true\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/app-settings", "host": ["{{api_url}}"], "path": ["app-settings"]}}, "response": []}, {"name": "Delete", "id": "********-84382802-872b-493d-9ad6-c2322e34dd7e", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": \"asjkhdkjash<PERSON>d\",\r\n    \"force\": false\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/app-settings", "host": ["{{api_url}}"], "path": ["app-settings"]}}, "response": []}, {"name": "Rest<PERSON>", "id": "********-50e6bd61-e56c-4097-8ba3-77f3f829c474", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": \"asjkhdkjash<PERSON>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/app-settings/restore", "host": ["{{api_url}}"], "path": ["app-settings", "restore"]}}, "response": []}], "id": "********-619b1791-48bc-4495-b03c-59c3268852b0"}, {"name": "<PERSON><PERSON>", "item": [{"name": "Get All", "id": "********-1bfcec3a-ef43-40ae-84b3-0b845c2777bd", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/carts", "host": ["{{api_url}}"], "path": ["carts"], "query": [{"key": "query", "value": "offset=0%26limit=5%26include%5B0%5D%5Bmodel%5D=user", "disabled": true}]}}, "response": []}, {"name": "Get My", "id": "********-0a2f711d-b445-41ee-819b-bb139d1640b1", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/carts/my", "host": ["{{api_url}}"], "path": ["carts", "my"], "query": [{"key": "query", "value": "offset=0%26limit=5%26include%5B0%5D%5Bmodel%5D=user", "disabled": true}]}}, "response": []}, {"name": "Get One", "id": "********-0f9aa5e4-220b-4353-b72e-0c59c2225826", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/products/get", "host": ["{{api_url}}"], "path": ["products", "get"], "query": [{"key": "query", "value": "search%5Bkeys%5D%5B0%5D=name%26search%5Bvalue%5D=bEDRId", "disabled": true}]}}, "response": []}, {"name": "Get By ID", "id": "********-b0b91501-9340-4ac9-891f-8b641802251f", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/products/akshdkjhaskjdhaksdhkjdhkasd", "host": ["{{api_url}}"], "path": ["products", "akshdkjhaskjdhaksdhkjdhkasd"], "query": [{"key": "query", "value": "include%5B0%5D%5Bmodel%5D=interest&include%5B1%5D%5Bmodel%5D=file&include%5B1%5D%5Balias%5D=profile", "disabled": true}]}}, "response": []}, {"name": "Create", "id": "********-b4e30f51-7bb3-4268-ba88-14f99515b220", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"user_id\": \"edf50692-1d5f-41b0-90bd-be712243df87\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/carts", "host": ["{{api_url}}"], "path": ["carts"]}}, "response": []}, {"name": "Update", "id": "********-5ffdd2ba-535d-48ec-99b9-e7b3ad30da6c", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": \"5fb3985b-8954-4396-9085-bc6001cd353a\",\r\n    \"access_rules\": [\r\n        \"read_access_rule\",\r\n        \"write_access_rule\",\r\n        \"read_role\",\r\n        \"write_role\",\r\n        \"read_user\",\r\n        \"write_user\",\r\n        \"read_destination\",\r\n        \"write_destination\",\r\n        \"read_vehicle\",\r\n        \"write_vehicle\",\r\n        \"read_association\",\r\n        \"write_association\",\r\n        \"read_level\",\r\n        \"write_level\",\r\n        \"read_plate_type\",\r\n        \"write_plate_type\",\r\n        \"read_seat_number\",\r\n        \"write_seat_number\",\r\n        \"read_station\",\r\n        \"write_station\",\r\n        \"read_trip\",\r\n        \"write_trip\",\r\n        \"read_route\",\r\n        \"write_route\",\r\n        \"read_config\",\r\n        \"write_config\",\r\n        \"read_action_log\",\r\n        \"write_action_log\",\r\n        \"sync_data\",\r\n        \"sync_trip\",\r\n        \"sync_vehicle\"\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/products", "host": ["{{api_url}}"], "path": ["products"]}}, "response": []}, {"name": "Delete", "id": "********-65068cd4-589c-4787-971e-42f3d176d2e6", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": \"asjkhdkjash<PERSON>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/products", "host": ["{{api_url}}"], "path": ["products"]}}, "response": []}, {"name": "Rest<PERSON>", "id": "********-db3b9a9e-2060-4873-a35d-493db149c762", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": \"asjkhdkjash<PERSON>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/products/restore", "host": ["{{api_url}}"], "path": ["products", "restore"]}}, "response": []}], "id": "********-df7854ea-9c62-4d63-9344-9fa4d11d820b"}, {"name": "<PERSON><PERSON>em", "item": [{"name": "Create", "id": "********-9332ac0d-fefa-4f21-a935-21e51125ecbc", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"cart_id\": \"carts[0].id\",\r\n    \"product_id\": \"products[0].id\",\r\n    \"quantity\": 1\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/cart-items", "host": ["{{api_url}}"], "path": ["cart-items"]}}, "response": []}, {"name": "Update", "id": "********-81dfa678-1f7a-4b9f-8151-ca71970a0f05", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": \"66dfe9c6-ab79-4a2f-a105-1dd5c4137036\",\r\n    \"createdAt\": \"2025-02-01T13:56:49.171Z\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/notification", "host": ["{{api_url}}"], "path": ["notification"]}}, "response": []}, {"name": "Get By Id", "id": "********-6c3be07a-b043-47d8-8a37-6e34006c4a14", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"key\": \"Health Status\",\r\n    \"value\": \"Healthy\",\r\n    \"type\": \"String\",\r\n    \"object_type\": \"String\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/notification/71b885bd-7e54-4ada-b499-c0fb575f4ac1", "host": ["{{api_url}}"], "path": ["notification", "71b885bd-7e54-4ada-b499-c0fb575f4ac1"]}}, "response": []}, {"name": "Get All", "id": "********-b2f26207-f69d-41ee-ab1b-aa49acc3d0fa", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"key\": \"Health Status\",\r\n    \"value\": \"Healthy\",\r\n    \"type\": \"String\",\r\n    \"object_type\": \"String\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/cart-items", "host": ["{{api_url}}"], "path": ["cart-items"]}}, "response": []}], "id": "********-8519a338-93c5-4918-9525-32e43364b873"}, {"name": "Category", "item": [{"name": "Get All", "id": "********-cb88532b-f095-4f44-9243-c6dbcbff2c4f", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/categories", "host": ["{{api_url}}"], "path": ["categories"], "query": [{"key": "query", "value": "offset=0%26limit=5%26include%5B0%5D%5Bmodel%5D=user", "disabled": true}]}}, "response": []}, {"name": "Get One", "id": "********-b42a8570-f7b9-4663-b6f1-ac5b81ed5cde", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/categories/get", "host": ["{{api_url}}"], "path": ["categories", "get"], "query": [{"key": "query", "value": "search%5Bkeys%5D%5B0%5D=name%26search%5Bvalue%5D=bEDRId", "disabled": true}]}}, "response": []}, {"name": "Get By ID", "id": "********-d6864d1c-5fcf-43b1-9d2f-781fda1bedd0", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/categories/akshdkjhaskjdhaksdhkjdhkasd", "host": ["{{api_url}}"], "path": ["categories", "akshdkjhaskjdhaksdhkjdhkasd"], "query": [{"key": "query", "value": "include%5B0%5D%5Bmodel%5D=interest&include%5B1%5D%5Bmodel%5D=file&include%5B1%5D%5Balias%5D=profile", "disabled": true}]}}, "response": []}, {"name": "Create", "id": "********-76ac5f96-c86c-4fa8-8892-85704705e443", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": "/C:/Users/<USER>/Pictures/1b381afa9fa99dfad302b3e159a507a3.jpg"}]}, "url": {"raw": "{{api_url}}/categories", "host": ["{{api_url}}"], "path": ["categories"]}}, "response": []}, {"name": "Update", "id": "********-9e98cca7-55e1-4e33-9e68-569131785760", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": \"5fb3985b-8954-4396-9085-bc6001cd353a\",\r\n    \"access_rules\": [\r\n        \"read_access_rule\",\r\n        \"write_access_rule\",\r\n        \"read_role\",\r\n        \"write_role\",\r\n        \"read_user\",\r\n        \"write_user\",\r\n        \"read_destination\",\r\n        \"write_destination\",\r\n        \"read_vehicle\",\r\n        \"write_vehicle\",\r\n        \"read_association\",\r\n        \"write_association\",\r\n        \"read_level\",\r\n        \"write_level\",\r\n        \"read_plate_type\",\r\n        \"write_plate_type\",\r\n        \"read_seat_number\",\r\n        \"write_seat_number\",\r\n        \"read_station\",\r\n        \"write_station\",\r\n        \"read_trip\",\r\n        \"write_trip\",\r\n        \"read_route\",\r\n        \"write_route\",\r\n        \"read_config\",\r\n        \"write_config\",\r\n        \"read_action_log\",\r\n        \"write_action_log\",\r\n        \"sync_data\",\r\n        \"sync_trip\",\r\n        \"sync_vehicle\"\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/categories", "host": ["{{api_url}}"], "path": ["categories"]}}, "response": []}, {"name": "Delete", "id": "********-e0297c49-576b-42b5-b0e5-7f3fa43d61cf", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": \"02283c2d-799f-4b9d-8e75-429c74fa203c\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/categories", "host": ["{{api_url}}"], "path": ["categories"]}}, "response": []}, {"name": "Rest<PERSON>", "id": "********-e8e12c68-a93d-4580-b665-bf652214e5a2", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": \"asjkhdkjash<PERSON>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/products/restore", "host": ["{{api_url}}"], "path": ["products", "restore"]}}, "response": []}], "id": "********-b699cbe7-6237-4198-905c-e73bda62b76f"}, {"name": "Favorite", "item": [{"name": "Get All", "id": "********-0cce3658-1509-4aa4-9f30-52eff3eeab2a", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/favorites", "host": ["{{api_url}}"], "path": ["favorites"], "query": [{"key": "query", "value": "offset=0%26limit=5%26include%5B0%5D%5Bmodel%5D=user", "disabled": true}]}}, "response": []}, {"name": "My Favorites", "id": "********-53b44879-eebb-4229-8372-a29b02541809", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/favorites/my", "host": ["{{api_url}}"], "path": ["favorites", "my"], "query": [{"key": "query", "value": "offset=0%26limit=5%26include%5B0%5D%5Bmodel%5D=user", "disabled": true}]}}, "response": []}, {"name": "Get One", "id": "********-1b05db83-3594-41f8-a343-c650374f92b9", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/orders/get", "host": ["{{api_url}}"], "path": ["orders", "get"], "query": [{"key": "query", "value": "search%5Bkeys%5D%5B0%5D=name%26search%5Bvalue%5D=bEDRId", "disabled": true}]}}, "response": []}, {"name": "Get By ID", "id": "********-b7d09cad-896a-416b-9979-b2dd9b3893bf", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/orders/akshdkjhaskjdhaksdhkjdhkasd", "host": ["{{api_url}}"], "path": ["orders", "akshdkjhaskjdhaksdhkjdhkasd"], "query": [{"key": "query", "value": "include%5B0%5D%5Bmodel%5D=interest&include%5B1%5D%5Bmodel%5D=file&include%5B1%5D%5Balias%5D=profile", "disabled": true}]}}, "response": []}, {"name": "Create", "id": "********-9d24c129-a666-48f4-9801-c05ade05a610", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"user_id\": \"121356tret5ter4\",\r\n    \"produc_id\": \"sakyd7asd6s86dsuyd87as6d8a7\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/favorites", "host": ["{{api_url}}"], "path": ["favorites"]}}, "response": []}, {"name": "Update", "id": "********-a444a7b3-ac1f-46af-9fd5-b70f6a642119", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": \"5fb3985b-8954-4396-9085-bc6001cd353a\",\r\n    \"order_number\": \"ORD001\",\r\n    \"status\": \"processing\",\r\n    \"address_id\": \"2312kk3j23k\",\r\n    \"payment_method_id\": \"kjsdhs6di8usidsdsu8ya87\",\r\n    \"subtotal\": 199.99,\r\n    \"shipping_fee\": 10,\r\n    \"tax\": 20,\r\n    \"total\": 229.99,\r\n    \"estimated_delivery\": \"new Date()\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/orders", "host": ["{{api_url}}"], "path": ["orders"]}}, "response": []}, {"name": "Delete", "id": "********-635c5403-32a3-460c-8ec0-17aabb31ae09", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": \"65de75b1-a9d0-4a35-aa2b-2c5131fb4240\",\r\n    \"force\": false\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/favorites", "host": ["{{api_url}}"], "path": ["favorites"]}}, "response": []}, {"name": "Rest<PERSON>", "id": "********-88a924c9-ec48-4401-9f6f-8e9f477f9176", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": \"asjkhdkjash<PERSON>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/orders/restore", "host": ["{{api_url}}"], "path": ["orders", "restore"]}}, "response": []}], "id": "********-fb5b75be-6e76-44ce-881c-1aa0146d9ce5"}, {"name": "Order", "item": [{"name": "Get All", "id": "********-0d6dd53a-2452-4197-b3f1-27f539ebba2c", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/orders", "host": ["{{api_url}}"], "path": ["orders"], "query": [{"key": "query", "value": "offset=0%26limit=5%26include%5B0%5D%5Bmodel%5D=user", "disabled": true}]}}, "response": []}, {"name": "Get One", "id": "********-f135d4c8-d007-434a-b2c9-edf6f211b7ac", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/orders/get", "host": ["{{api_url}}"], "path": ["orders", "get"], "query": [{"key": "query", "value": "search%5Bkeys%5D%5B0%5D=name%26search%5Bvalue%5D=bEDRId", "disabled": true}]}}, "response": []}, {"name": "Get By ID", "id": "********-764d821a-3723-4100-bf47-1e325bd6ea88", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/orders/akshdkjhaskjdhaksdhkjdhkasd", "host": ["{{api_url}}"], "path": ["orders", "akshdkjhaskjdhaksdhkjdhkasd"], "query": [{"key": "query", "value": "include%5B0%5D%5Bmodel%5D=interest&include%5B1%5D%5Bmodel%5D=file&include%5B1%5D%5Balias%5D=profile", "disabled": true}]}}, "response": []}, {"name": "Create", "id": "********-c8924fca-5b91-4ab7-b564-2cc388042ddc", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"user_id\": \"121356tret5ter4\",\r\n    \"order_number\": \"ORD001\",\r\n    \"status\": \"processing\", \"address_id\": \"2312kk3j23k\",\r\n    \"payment_method_id\": \"kjsdhs6di8usidsdsu8ya87\",\r\n    \"subtotal\": 199.99,\r\n    \"shipping_fee\": 10,\r\n    \"tax\": 20,\r\n    \"total\": 229.99,\r\n    \"estimated_delivery\": \"new Date()\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/orders", "host": ["{{api_url}}"], "path": ["orders"]}}, "response": []}, {"name": "Update", "id": "********-6f63d4eb-40b3-415e-8665-bc779ec9c3ff", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": \"5fb3985b-8954-4396-9085-bc6001cd353a\",\r\n    \"order_number\": \"ORD001\",\r\n    \"status\": \"processing\",\r\n    \"address_id\": \"2312kk3j23k\",\r\n    \"payment_method_id\": \"kjsdhs6di8usidsdsu8ya87\",\r\n    \"subtotal\": 199.99,\r\n    \"shipping_fee\": 10,\r\n    \"tax\": 20,\r\n    \"total\": 229.99,\r\n    \"estimated_delivery\": \"new Date()\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/orders", "host": ["{{api_url}}"], "path": ["orders"]}}, "response": []}, {"name": "Delete", "id": "********-d04fd625-ebd5-495e-a9d8-ba63bbe707e3", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": \"asjkhdkjash<PERSON>d\",\r\n    \"force\": false\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/orders", "host": ["{{api_url}}"], "path": ["orders"]}}, "response": []}, {"name": "Rest<PERSON>", "id": "********-96588d7b-fb3d-4dc5-95a5-641c6bdbcb94", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": \"asjkhdkjash<PERSON>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/orders/restore", "host": ["{{api_url}}"], "path": ["orders", "restore"]}}, "response": []}], "id": "********-c1f8c1a0-8eda-4913-8f5e-1d00665eaecd"}, {"name": "Order Item", "item": [{"name": "Create", "id": "********-7fd3bc3c-20d2-40d4-b371-d8c3ab9dff23", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"order_id\": \"orders[1].id\",\r\n    \"product_id\": \"products[1].id\",\r\n    \"quantity\": 1,\r\n    \"price\": 349.99\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/order-items", "host": ["{{api_url}}"], "path": ["order-items"]}}, "response": []}, {"name": "Update", "id": "********-f144104a-fcf3-49d2-8f87-4567feca6f28", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": \"66dfe9c6-ab79-4a2f-a105-1dd5c4137036\",\r\n    \"createdAt\": \"2025-02-01T13:56:49.171Z\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/notification", "host": ["{{api_url}}"], "path": ["notification"]}}, "response": []}, {"name": "Get By Id", "id": "********-5019679c-fe7f-430f-8c1d-76a52dff1059", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"key\": \"Health Status\",\r\n    \"value\": \"Healthy\",\r\n    \"type\": \"String\",\r\n    \"object_type\": \"String\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/notification/71b885bd-7e54-4ada-b499-c0fb575f4ac1", "host": ["{{api_url}}"], "path": ["notification", "71b885bd-7e54-4ada-b499-c0fb575f4ac1"]}}, "response": []}, {"name": "Get All", "id": "********-e9f7fb93-39fa-482e-8630-10f6f3a36f44", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"key\": \"Health Status\",\r\n    \"value\": \"Healthy\",\r\n    \"type\": \"String\",\r\n    \"object_type\": \"String\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/order-items", "host": ["{{api_url}}"], "path": ["order-items"]}}, "response": []}], "id": "********-b91812ba-ba89-4bff-8992-e8f6c40e0f2a"}, {"name": "Order Tracking", "item": [{"name": "Create", "id": "********-fe97b705-a8d2-4401-962e-a17be3e70f31", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"user_id\": \"3ef7a016-f8be-47d2-b8d5-8ad861eab87e\",\r\n    \"notification_title\": \"Subscription Started\",\r\n    \"notification_body\": \"Your subscription for 3 listings is being processed. You will be charged $95.97 per month.\",\r\n    \"notification_type\": \"Success\",\r\n    \"is_read\": false,\r\n    \"notification_date\": \"2025-02-06T16:50:50.358Z\",\r\n    \"notification_url\": \"http://boingo.ai/subscriptions\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/notification", "host": ["{{api_url}}"], "path": ["notification"]}}, "response": []}, {"name": "Update", "id": "********-555b93d5-2ee2-462e-909a-46cac72762ec", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": \"66dfe9c6-ab79-4a2f-a105-1dd5c4137036\",\r\n    \"createdAt\": \"2025-02-01T13:56:49.171Z\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/notification", "host": ["{{api_url}}"], "path": ["notification"]}}, "response": []}, {"name": "Get By Id", "id": "********-9854df13-1129-45f3-af0d-7b088e2dd649", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"key\": \"Health Status\",\r\n    \"value\": \"Healthy\",\r\n    \"type\": \"String\",\r\n    \"object_type\": \"String\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/notification/71b885bd-7e54-4ada-b499-c0fb575f4ac1", "host": ["{{api_url}}"], "path": ["notification", "71b885bd-7e54-4ada-b499-c0fb575f4ac1"]}}, "response": []}, {"name": "Get All", "id": "********-9babb0a3-85a8-4c09-ad0b-f5a2756a62df", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"key\": \"Health Status\",\r\n    \"value\": \"Healthy\",\r\n    \"type\": \"String\",\r\n    \"object_type\": \"String\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/notification", "host": ["{{api_url}}"], "path": ["notification"]}}, "response": []}], "id": "********-3f06117b-6d2d-4022-af8b-db6c18476692"}, {"name": "Payment Method", "item": [{"name": "Get All", "id": "********-54d8dc0c-ef1f-4e47-8cfa-94314c90798d", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/payment-method", "host": ["{{api_url}}"], "path": ["payment-method"], "query": [{"key": "query", "value": "offset=0%26limit=5%26include%5B0%5D%5Bmodel%5D=user", "disabled": true}]}}, "response": []}, {"name": "Get One", "id": "********-ffc810ed-c95e-449e-8f58-b403008e144b", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/products/get", "host": ["{{api_url}}"], "path": ["products", "get"], "query": [{"key": "query", "value": "search%5Bkeys%5D%5B0%5D=name%26search%5Bvalue%5D=bEDRId", "disabled": true}]}}, "response": []}, {"name": "Get By ID", "id": "********-e0aa2fb3-4d1f-44f8-bdd6-a00af3bcf72e", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/products/akshdkjhaskjdhaksdhkjdhkasd", "host": ["{{api_url}}"], "path": ["products", "akshdkjhaskjdhaksdhkjdhkasd"], "query": [{"key": "query", "value": "include%5B0%5D%5Bmodel%5D=interest&include%5B1%5D%5Bmodel%5D=file&include%5B1%5D%5Balias%5D=profile", "disabled": true}]}}, "response": []}, {"name": "Create", "id": "********-f533a60c-0fef-4a0d-ba97-431a71e6e365", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"user_id\": \"42f4b746-a3f5-49a0-ac08-57cbe91cc898\",\r\n    \"type\": \"tele_birr\",\r\n    \"provider\": \"ethio tel\",\r\n    \"account_number\": \"5678\",\r\n    \"expiry_date\": \"12/25\",\r\n    \"address_id\": \"93e7d4ee-bb59-47c1-8422-4e512e312e15\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/payment-method", "host": ["{{api_url}}"], "path": ["payment-method"]}}, "response": []}, {"name": "Update", "id": "********-da197190-c32a-4adc-930b-8a1c246c3e0d", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": \"5fb3985b-8954-4396-9085-bc6001cd353a\",\r\n    \"access_rules\": [\r\n        \"read_access_rule\",\r\n        \"write_access_rule\",\r\n        \"read_role\",\r\n        \"write_role\",\r\n        \"read_user\",\r\n        \"write_user\",\r\n        \"read_destination\",\r\n        \"write_destination\",\r\n        \"read_vehicle\",\r\n        \"write_vehicle\",\r\n        \"read_association\",\r\n        \"write_association\",\r\n        \"read_level\",\r\n        \"write_level\",\r\n        \"read_plate_type\",\r\n        \"write_plate_type\",\r\n        \"read_seat_number\",\r\n        \"write_seat_number\",\r\n        \"read_station\",\r\n        \"write_station\",\r\n        \"read_trip\",\r\n        \"write_trip\",\r\n        \"read_route\",\r\n        \"write_route\",\r\n        \"read_config\",\r\n        \"write_config\",\r\n        \"read_action_log\",\r\n        \"write_action_log\",\r\n        \"sync_data\",\r\n        \"sync_trip\",\r\n        \"sync_vehicle\"\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/products", "host": ["{{api_url}}"], "path": ["products"]}}, "response": []}, {"name": "Delete", "id": "********-00b92ec4-4111-40e9-8269-a8cc99e6da30", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": \"asjkhdkjash<PERSON>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/products", "host": ["{{api_url}}"], "path": ["products"]}}, "response": []}, {"name": "Rest<PERSON>", "id": "********-898c0bdb-ba91-4170-a50d-e590c018650d", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": \"asjkhdkjash<PERSON>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/products/restore", "host": ["{{api_url}}"], "path": ["products", "restore"]}}, "response": []}], "id": "********-2dd3eb0f-3d7c-49e4-aa0c-08282876dc56"}, {"name": "<PERSON><PERSON>", "item": [{"name": "Get All", "id": "********-81870ac7-7aa3-4698-be2b-3e3ec0146784", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/api/v1/chappa-payments", "host": ["{{api_url}}"], "path": ["api", "v1", "chappa-payments"], "query": [{"key": "query", "value": "offset=0%26limit=5%26include%5B0%5D%5Bmodel%5D=user", "disabled": true}]}}, "response": []}, {"name": "Get One", "id": "********-2a01db55-8396-42fe-86f3-74b174d79e8a", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/products/get", "host": ["{{api_url}}"], "path": ["products", "get"], "query": [{"key": "query", "value": "search%5Bkeys%5D%5B0%5D=name%26search%5Bvalue%5D=bEDRId", "disabled": true}]}}, "response": []}, {"name": "Get By ID", "id": "********-8dc44b01-2bac-498f-a6a4-f824e5df2230", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/products/akshdkjhaskjdhaksdhkjdhkasd", "host": ["{{api_url}}"], "path": ["products", "akshdkjhaskjdhaksdhkjdhkasd"], "query": [{"key": "query", "value": "include%5B0%5D%5Bmodel%5D=interest&include%5B1%5D%5Bmodel%5D=file&include%5B1%5D%5Balias%5D=profile", "disabled": true}]}}, "response": []}, {"name": "Initiate", "id": "********-02140832-3d0c-4721-b153-21502f2fc5fa", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"order_id\": \"66f2adf2-e522-4bce-8511-cdc73355a84d\",\r\n    \"payment_method_id\": \"e2e859a0-f6d6-4da8-bff1-47c7261751b5\",\r\n    \"amount\": 100,\r\n    \"callback_url\": \"http://localhost:5000\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/api/v1/chappa-payments/initiate", "host": ["{{api_url}}"], "path": ["api", "v1", "chappa-payments", "initiate"]}}, "response": []}, {"name": "Verify", "id": "********-800e0bda-8be1-4469-ae99-6405a15314ef", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"tx_ref\": \"tx-124ad9fd-3709-44dd-9bbe-4a8900fff521\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/api/v1/chappa-payments/verify", "host": ["{{api_url}}"], "path": ["api", "v1", "chappa-payments", "verify"]}}, "response": []}, {"name": "Update", "id": "********-1bf74fc2-97a6-4897-affe-0d055fa09f35", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": \"5fb3985b-8954-4396-9085-bc6001cd353a\",\r\n    \"access_rules\": [\r\n        \"read_access_rule\",\r\n        \"write_access_rule\",\r\n        \"read_role\",\r\n        \"write_role\",\r\n        \"read_user\",\r\n        \"write_user\",\r\n        \"read_destination\",\r\n        \"write_destination\",\r\n        \"read_vehicle\",\r\n        \"write_vehicle\",\r\n        \"read_association\",\r\n        \"write_association\",\r\n        \"read_level\",\r\n        \"write_level\",\r\n        \"read_plate_type\",\r\n        \"write_plate_type\",\r\n        \"read_seat_number\",\r\n        \"write_seat_number\",\r\n        \"read_station\",\r\n        \"write_station\",\r\n        \"read_trip\",\r\n        \"write_trip\",\r\n        \"read_route\",\r\n        \"write_route\",\r\n        \"read_config\",\r\n        \"write_config\",\r\n        \"read_action_log\",\r\n        \"write_action_log\",\r\n        \"sync_data\",\r\n        \"sync_trip\",\r\n        \"sync_vehicle\"\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/products", "host": ["{{api_url}}"], "path": ["products"]}}, "response": []}, {"name": "Delete", "id": "********-2187ec5e-8339-4dd6-9ea3-c836091a6350", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": \"asjkhdkjash<PERSON>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/products", "host": ["{{api_url}}"], "path": ["products"]}}, "response": []}, {"name": "Rest<PERSON>", "id": "********-e9afaccf-e328-4410-b90a-af1b8b8ba560", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": \"asjkhdkjash<PERSON>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/products/restore", "host": ["{{api_url}}"], "path": ["products", "restore"]}}, "response": []}], "id": "********-7cd0a927-1689-4ea4-892a-d54f71898b71"}, {"name": "Product", "item": [{"name": "Get All", "id": "********-16a0c5f0-c0e0-4409-80b1-6d37efa3bdf0", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/products", "host": ["{{api_url}}"], "path": ["products"], "query": [{"key": "query", "value": "offset=0%26limit=5%26include%5B0%5D%5Bmodel%5D=user", "disabled": true}]}}, "response": []}, {"name": "Get One", "id": "********-f734dab5-4873-46fd-a1b1-c86d11735da4", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/products/get", "host": ["{{api_url}}"], "path": ["products", "get"], "query": [{"key": "query", "value": "search%5Bkeys%5D%5B0%5D=name%26search%5Bvalue%5D=bEDRId", "disabled": true}]}}, "response": []}, {"name": "Search", "id": "********-aa3b9d03-cf75-4dc2-8f9b-c5a5837e4bca", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/products/search?query=Wireless&limit=10", "host": ["{{api_url}}"], "path": ["products", "search"], "query": [{"key": "query", "value": "Wireless"}, {"key": "limit", "value": "10"}]}}, "response": []}, {"name": "Trending", "id": "********-8ee3d707-063e-4838-b60b-768e5e660e08", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/products/trending", "host": ["{{api_url}}"], "path": ["products", "trending"], "query": [{"key": "query", "value": "search%5Bkeys%5D%5B0%5D=name%26search%5Bvalue%5D=bEDRId", "disabled": true}]}}, "response": []}, {"name": "Featured", "id": "********-acfe5be8-ebdb-45e5-8f0f-871228e8c2cd", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/products/featured", "host": ["{{api_url}}"], "path": ["products", "featured"], "query": [{"key": "query", "value": "search%5Bkeys%5D%5B0%5D=name%26search%5Bvalue%5D=bEDRId", "disabled": true}]}}, "response": []}, {"name": "Get By ID", "id": "********-266fdb5f-a3dc-4d17-8a56-2246668c5f73", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/products/akshdkjhaskjdhaksdhkjdhkasd", "host": ["{{api_url}}"], "path": ["products", "akshdkjhaskjdhaksdhkjdhkasd"], "query": [{"key": "query", "value": "include%5B0%5D%5Bmodel%5D=interest&include%5B1%5D%5Bmodel%5D=file&include%5B1%5D%5Balias%5D=profile", "disabled": true}]}}, "response": []}, {"name": "Get Recently Viewed", "id": "********-b5ac66c8-7444-45b5-a1a3-01fb78fd4f54", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/products/recently-viewed", "host": ["{{api_url}}"], "path": ["products", "recently-viewed"], "query": [{"key": "query", "value": "include%5B0%5D%5Bmodel%5D=interest&include%5B1%5D%5Bmodel%5D=file&include%5B1%5D%5Balias%5D=profile", "disabled": true}]}}, "response": []}, {"name": "Get By ID With Last View", "id": "********-f3fff8c6-3472-4e9c-a93b-03e7fbd2ec59", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "url": {"raw": "{{api_url}}/products/268f197c-0b5a-469c-811a-9ecd773a4887", "host": ["{{api_url}}"], "path": ["products", "268f197c-0b5a-469c-811a-9ecd773a4887"], "query": [{"key": "query", "value": "include%5B0%5D%5Bmodel%5D=interest&include%5B1%5D%5Bmodel%5D=file&include%5B1%5D%5Balias%5D=profile", "disabled": true}]}}, "response": []}, {"name": "Create", "id": "********-1684615f-a1ec-4ce6-a314-12724fc420c8", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": "/C:/Users/<USER>/Pictures/1b381afa9fa99dfad302b3e159a507a3.jpg"}]}, "url": {"raw": "{{api_url}}/products", "host": ["{{api_url}}"], "path": ["products"]}}, "response": []}, {"name": "Register", "id": "********-bd24b188-c7f0-46ac-afcf-14d14afdeaad", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"product\": {\r\n        \"name\": \"Wireless Bluetooth Mouse\",\r\n        \"description\": \"High-quality wireless mouse with smooth experience\",\r\n        \"price\": \"199.99\",\r\n        \"stock\": 50,\r\n        \"featured\": true,\r\n        \"trending\": true,\r\n        \"category_id\": \"392621c2-acf2-4f37-84ca-3796f57fe900\"\r\n    },\r\n    \"files\": [\r\n        \"69e2ed50-2ad5-4637-9deb-eb8684bf8acd\",\r\n        \"491378e2-733e-407c-bb95-b94f7c86f0d4\"\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/products/register", "host": ["{{api_url}}"], "path": ["products", "register"]}}, "response": []}, {"name": "Update", "id": "********-ab532309-5872-4faa-80bc-43896098e746", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": \"5fb3985b-8954-4396-9085-bc6001cd353a\",\r\n    \"access_rules\": [\r\n        \"read_access_rule\",\r\n        \"write_access_rule\",\r\n        \"read_role\",\r\n        \"write_role\",\r\n        \"read_user\",\r\n        \"write_user\",\r\n        \"read_destination\",\r\n        \"write_destination\",\r\n        \"read_vehicle\",\r\n        \"write_vehicle\",\r\n        \"read_association\",\r\n        \"write_association\",\r\n        \"read_level\",\r\n        \"write_level\",\r\n        \"read_plate_type\",\r\n        \"write_plate_type\",\r\n        \"read_seat_number\",\r\n        \"write_seat_number\",\r\n        \"read_station\",\r\n        \"write_station\",\r\n        \"read_trip\",\r\n        \"write_trip\",\r\n        \"read_route\",\r\n        \"write_route\",\r\n        \"read_config\",\r\n        \"write_config\",\r\n        \"read_action_log\",\r\n        \"write_action_log\",\r\n        \"sync_data\",\r\n        \"sync_trip\",\r\n        \"sync_vehicle\"\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/products", "host": ["{{api_url}}"], "path": ["products"]}}, "response": []}, {"name": "Delete", "id": "********-f371a988-5bcf-400f-bd35-241d9a939c06", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": \"asjkhdkjash<PERSON>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/products", "host": ["{{api_url}}"], "path": ["products"]}}, "response": []}, {"name": "Rest<PERSON>", "id": "********-3f3890f1-1ccb-40fc-9f70-c20a29ba815e", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": \"asjkhdkjash<PERSON>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/products/restore", "host": ["{{api_url}}"], "path": ["products", "restore"]}}, "response": []}], "id": "********-bceb7295-3d46-40de-99f5-d449296f8c03"}, {"name": "User Product View", "item": [{"name": "Get All", "id": "********-b6d2ce4f-f108-4eda-98f9-a19b6d74eaa4", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/products", "host": ["{{api_url}}"], "path": ["products"], "query": [{"key": "query", "value": "offset=0%26limit=5%26include%5B0%5D%5Bmodel%5D=user", "disabled": true}]}}, "response": []}, {"name": "Get One", "id": "********-f3dbcd05-42fa-4526-8974-70966078fa91", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/products/get", "host": ["{{api_url}}"], "path": ["products", "get"], "query": [{"key": "query", "value": "search%5Bkeys%5D%5B0%5D=name%26search%5Bvalue%5D=bEDRId", "disabled": true}]}}, "response": []}, {"name": "Get By ID", "id": "********-212f054e-e60b-4a35-a367-fde772282005", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/products/akshdkjhaskjdhaksdhkjdhkasd", "host": ["{{api_url}}"], "path": ["products", "akshdkjhaskjdhaksdhkjdhkasd"], "query": [{"key": "query", "value": "include%5B0%5D%5Bmodel%5D=interest&include%5B1%5D%5Bmodel%5D=file&include%5B1%5D%5Balias%5D=profile", "disabled": true}]}}, "response": []}, {"name": "Create", "id": "********-908a34c9-bea4-4c40-887f-dac671df8e7b", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": "/C:/Users/<USER>/Pictures/1b381afa9fa99dfad302b3e159a507a3.jpg"}]}, "url": {"raw": "{{api_url}}/products", "host": ["{{api_url}}"], "path": ["products"]}}, "response": []}, {"name": "Register", "id": "********-acaceb99-34d9-4e06-a33e-4c47d652293e", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"product\": {\r\n        \"name\": \"Wireless Bluetooth Mouse\",\r\n        \"description\": \"High-quality wireless mouse with smooth experience\",\r\n        \"price\": \"199.99\",\r\n        \"stock\": 50,\r\n        \"featured\": true,\r\n        \"trending\": true,\r\n        \"category_id\": \"392621c2-acf2-4f37-84ca-3796f57fe900\"\r\n    },\r\n    \"files\": [\r\n        \"69e2ed50-2ad5-4637-9deb-eb8684bf8acd\",\r\n        \"491378e2-733e-407c-bb95-b94f7c86f0d4\"\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/products/register", "host": ["{{api_url}}"], "path": ["products", "register"]}}, "response": []}, {"name": "Update", "id": "********-57417281-0a13-41db-8721-c4492fa1ddd2", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": \"5fb3985b-8954-4396-9085-bc6001cd353a\",\r\n    \"access_rules\": [\r\n        \"read_access_rule\",\r\n        \"write_access_rule\",\r\n        \"read_role\",\r\n        \"write_role\",\r\n        \"read_user\",\r\n        \"write_user\",\r\n        \"read_destination\",\r\n        \"write_destination\",\r\n        \"read_vehicle\",\r\n        \"write_vehicle\",\r\n        \"read_association\",\r\n        \"write_association\",\r\n        \"read_level\",\r\n        \"write_level\",\r\n        \"read_plate_type\",\r\n        \"write_plate_type\",\r\n        \"read_seat_number\",\r\n        \"write_seat_number\",\r\n        \"read_station\",\r\n        \"write_station\",\r\n        \"read_trip\",\r\n        \"write_trip\",\r\n        \"read_route\",\r\n        \"write_route\",\r\n        \"read_config\",\r\n        \"write_config\",\r\n        \"read_action_log\",\r\n        \"write_action_log\",\r\n        \"sync_data\",\r\n        \"sync_trip\",\r\n        \"sync_vehicle\"\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/products", "host": ["{{api_url}}"], "path": ["products"]}}, "response": []}, {"name": "Delete", "id": "********-c646e767-502f-4572-8045-94bc24a4ced5", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": \"asjkhdkjash<PERSON>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/products", "host": ["{{api_url}}"], "path": ["products"]}}, "response": []}, {"name": "Rest<PERSON>", "id": "********-ec9bb22c-7102-4a09-8c88-d074285b9d61", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": \"asjkhdkjash<PERSON>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/products/restore", "host": ["{{api_url}}"], "path": ["products", "restore"]}}, "response": []}], "id": "********-20b7844a-71e6-4ec6-92a6-51391d8cd79d"}, {"name": "Product Image", "item": [{"name": "Get All", "id": "********-11b091a6-d5bc-4942-a453-53b5ba3fa1e0", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/orders", "host": ["{{api_url}}"], "path": ["orders"], "query": [{"key": "query", "value": "offset=0%26limit=5%26include%5B0%5D%5Bmodel%5D=user", "disabled": true}]}}, "response": []}, {"name": "Get One", "id": "********-d7941688-ae28-4e7f-bd50-02a9edc7d85a", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/orders/get", "host": ["{{api_url}}"], "path": ["orders", "get"], "query": [{"key": "query", "value": "search%5Bkeys%5D%5B0%5D=name%26search%5Bvalue%5D=bEDRId", "disabled": true}]}}, "response": []}, {"name": "Get By ID", "id": "********-07e128fd-b51a-4f30-8c2c-afb07f14d6d8", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{api_url}}/orders/akshdkjhaskjdhaksdhkjdhkasd", "host": ["{{api_url}}"], "path": ["orders", "akshdkjhaskjdhaksdhkjdhkasd"], "query": [{"key": "query", "value": "include%5B0%5D%5Bmodel%5D=interest&include%5B1%5D%5Bmodel%5D=file&include%5B1%5D%5Balias%5D=profile", "disabled": true}]}}, "response": []}, {"name": "Create", "id": "********-4d5ffcf2-6c6b-44e0-8edc-ef90168ee68c", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"user_id\": \"121356tret5ter4\",\r\n    \"order_number\": \"ORD001\",\r\n    \"status\": \"processing\", \"address_id\": \"2312kk3j23k\",\r\n    \"payment_method_id\": \"kjsdhs6di8usidsdsu8ya87\",\r\n    \"subtotal\": 199.99,\r\n    \"shipping_fee\": 10,\r\n    \"tax\": 20,\r\n    \"total\": 229.99,\r\n    \"estimated_delivery\": \"new Date()\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/orders", "host": ["{{api_url}}"], "path": ["orders"]}}, "response": []}, {"name": "Update", "id": "********-9cf32187-4e54-48d1-974d-dbfa9f387fdc", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": \"5fb3985b-8954-4396-9085-bc6001cd353a\",\r\n    \"order_number\": \"ORD001\",\r\n    \"status\": \"processing\",\r\n    \"address_id\": \"2312kk3j23k\",\r\n    \"payment_method_id\": \"kjsdhs6di8usidsdsu8ya87\",\r\n    \"subtotal\": 199.99,\r\n    \"shipping_fee\": 10,\r\n    \"tax\": 20,\r\n    \"total\": 229.99,\r\n    \"estimated_delivery\": \"new Date()\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/orders", "host": ["{{api_url}}"], "path": ["orders"]}}, "response": []}, {"name": "Delete", "id": "********-0971cb93-0117-4431-b983-d7e3ea7a1522", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": \"asjkhdkjash<PERSON>d\",\r\n    \"force\": false\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/orders", "host": ["{{api_url}}"], "path": ["orders"]}}, "response": []}, {"name": "Rest<PERSON>", "id": "********-66196db3-5f16-41e9-8e9c-7cad15dadef4", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": \"asjkhdkjash<PERSON>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/orders/restore", "host": ["{{api_url}}"], "path": ["orders", "restore"]}}, "response": []}], "id": "********-d0a54054-5966-4be4-9caa-ddc943f18a10"}, {"name": "Search History", "item": [{"name": "Create", "id": "********-179c2ba6-fcac-4273-9af0-2430106d6752", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"user_id\": \"3ef7a016-f8be-47d2-b8d5-8ad861eab87e\",\r\n    \"notification_title\": \"Subscription Started\",\r\n    \"notification_body\": \"Your subscription for 3 listings is being processed. You will be charged $95.97 per month.\",\r\n    \"notification_type\": \"Success\",\r\n    \"is_read\": false,\r\n    \"notification_date\": \"2025-02-06T16:50:50.358Z\",\r\n    \"notification_url\": \"http://boingo.ai/subscriptions\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/notification", "host": ["{{api_url}}"], "path": ["notification"]}}, "response": []}, {"name": "Update", "id": "********-349dace7-302c-4797-8600-7591d1adadcc", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": \"66dfe9c6-ab79-4a2f-a105-1dd5c4137036\",\r\n    \"createdAt\": \"2025-02-01T13:56:49.171Z\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/notification", "host": ["{{api_url}}"], "path": ["notification"]}}, "response": []}, {"name": "Get By Id", "id": "********-2169106e-870a-4798-be01-b5b1429b737f", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"key\": \"Health Status\",\r\n    \"value\": \"Healthy\",\r\n    \"type\": \"String\",\r\n    \"object_type\": \"String\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/notification/71b885bd-7e54-4ada-b499-c0fb575f4ac1", "host": ["{{api_url}}"], "path": ["notification", "71b885bd-7e54-4ada-b499-c0fb575f4ac1"]}}, "response": []}, {"name": "Get All", "id": "********-9ddfdcf8-037d-4dd4-bed8-973de971413a", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"key\": \"Health Status\",\r\n    \"value\": \"Healthy\",\r\n    \"type\": \"String\",\r\n    \"object_type\": \"String\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/notification", "host": ["{{api_url}}"], "path": ["notification"]}}, "response": []}, {"name": "Filter By Date", "id": "********-8da88f31-9fe7-4448-ab9c-628df2beefae", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"is_read\": \"false\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/notification/filter?query=offset%3D0%253An%26limit%3D5%253An%26order%255B0%255D%255Bkey%255D%3DcreatedAt%253As%26order%255B0%255D%255Bvalue%255D%3DDESC%253As", "host": ["{{api_url}}"], "path": ["notification", "filter"], "query": [{"key": "query", "value": "offset%3D0%253An%26limit%3D5%253An%26order%255B0%255D%255Bkey%255D%3DcreatedAt%253As%26order%255B0%255D%255Bvalue%255D%3DDESC%253As"}]}}, "response": []}, {"name": "Get My Notification", "id": "********-675b9bcf-ef9b-4121-962b-35cb727846e7", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"key\": \"Health Status\",\r\n    \"value\": \"Healthy\",\r\n    \"type\": \"String\",\r\n    \"object_type\": \"String\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api_url}}/notification/my", "host": ["{{api_url}}"], "path": ["notification", "my"]}}, "response": []}], "id": "********-f9beed29-da06-427e-96ee-e59048e23ea7"}], "id": "********-119675e9-58ac-40c7-b0da-0421f61f94b1"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"id": "0f6f6221-be9a-4d47-808a-394ed84d28fc", "type": "text/javascript", "packages": {}, "exec": [""]}}, {"listen": "test", "script": {"id": "33bbe38d-7c73-4c65-b325-ee5bc01365e4", "type": "text/javascript", "packages": {}, "exec": [""]}}]}