const { Sequelize } = require('sequelize');

// Database connection
const sequelize = new Sequelize('marketplace', 'postgres', 'root', {
  host: 'localhost',
  dialect: 'postgres',
  logging: console.log
});

async function checkDatabase() {
  try {
    // Test connection
    await sequelize.authenticate();
    console.log('✅ Database connection successful');

    // Check if banners table exists and describe its structure
    const [results] = await sequelize.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'banners'
      ORDER BY ordinal_position;
    `);

    console.log('\n📋 Banners table structure:');
    console.table(results);

    // Check if there are any records
    const [countResult] = await sequelize.query('SELECT COUNT(*) as count FROM banners');
    console.log(`\n📊 Total records in banners table: ${countResult[0].count}`);

    // Try to select a few records using camelCase
    const [records] = await sequelize.query('SELECT id, title, status, "createdAt", "updatedAt" FROM banners LIMIT 3');
    console.log('\n📄 Sample records:');
    console.table(records);

  } catch (error) {
    console.error('❌ Database error:', error.message);
  } finally {
    await sequelize.close();
  }
}

checkDatabase();
