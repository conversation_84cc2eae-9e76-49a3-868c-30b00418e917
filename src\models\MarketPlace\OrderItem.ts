import { DataTypes, Model, type Sequelize } from "sequelize"

export class OrderI<PERSON> extends Model {
  public id!: string
  public order_id!: string
  public product_id!: string
  public quantity!: number
  public price!: number // Price at time of purchase

  public readonly createdAt!: Date
  public readonly updatedAt!: Date
}

export default (sequelize: Sequelize) => {
  OrderItem.init(
    {
      id: {
        type: DataTypes.UUID,
        primaryKey: true,
        allowNull: false,
        defaultValue: DataTypes.UUIDV4,
      },
      order_id: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      product_id: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      quantity: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      price: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: false,
      },
    },
    {
      sequelize,
      paranoid: true,
      modelName: "order_item",
      tableName: "order_items",
    },
  )

  return OrderItem
}
