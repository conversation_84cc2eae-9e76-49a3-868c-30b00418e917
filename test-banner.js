// Comprehensive test script to verify banner functionality
const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

// Test credentials (from seed data)
const ADMIN_CREDENTIALS = {
  email: "<EMAIL>",
  password: "#Include29"
};

let authToken = null;

async function login() {
  console.log('🔐 Logging in as admin...');
  try {
    const response = await axios.post(`${BASE_URL}/auth/login`, ADMIN_CREDENTIALS);
    authToken = response.data.data.token;
    console.log('✅ Login successful');
    console.log(`   Token: ${authToken.substring(0, 20)}...\n`);
    return true;
  } catch (error) {
    console.log('❌ Login failed');
    console.log(`   Error: ${error.response?.status} - ${error.response?.statusText}`);
    console.log(`   Message: ${error.response?.data?.message || error.message}\n`);
    return false;
  }
}

async function testBannerEndpoints() {
  console.log('🎯 Testing Banner Endpoints...\n');

  try {
    // Test 1: Get all active banners (public endpoint)
    console.log('1. Testing GET /banners (public endpoint)');
    try {
      const response = await axios.get(`${BASE_URL}/banners`);
      console.log('✅ Public banners endpoint accessible');
      console.log(`   Status: ${response.status}`);
      console.log(`   Active banners found: ${response.data.data?.rows?.length || 0}`);
      if (response.data.data?.rows?.length > 0) {
        console.log(`   First banner: ${response.data.data.rows[0].title}`);
      }
      console.log('');
    } catch (error) {
      console.log('❌ Public banners endpoint failed');
      console.log(`   Error: ${error.response?.status} - ${error.response?.statusText}`);
      console.log(`   Message: ${error.response?.data?.message || error.message}\n`);
    }

    // Test 2: Try to access admin endpoint without authentication
    console.log('2. Testing GET /banners/admin (without auth)');
    try {
      const response = await axios.get(`${BASE_URL}/banners/admin`);
      console.log('❌ Admin endpoint should require authentication');
      console.log(`   Status: ${response.status}\n`);
    } catch (error) {
      if (error.response?.status === 401 || error.response?.status === 403) {
        console.log('✅ Admin endpoint properly protected');
        console.log(`   Status: ${error.response.status} - ${error.response.statusText}\n`);
      } else {
        console.log('❌ Unexpected error on admin endpoint');
        console.log(`   Error: ${error.response?.status} - ${error.response?.statusText}\n`);
      }
    }

    // Test 3: Try to create a banner without authentication
    console.log('3. Testing POST /banners/admin (without auth)');
    const testBanner = {
      title: 'Test Banner',
      description: 'Test Description',
      image_url: 'https://example.com/banner.jpg',
      link_url: 'https://example.com',
      start_date: new Date().toISOString(),
      end_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
      priority: 50,
      target_audience: 'ALL'
    };

    try {
      const response = await axios.post(`${BASE_URL}/banners/admin`, testBanner);
      console.log('❌ Create endpoint should require authentication');
      console.log(`   Status: ${response.status}\n`);
    } catch (error) {
      if (error.response?.status === 401 || error.response?.status === 403) {
        console.log('✅ Create endpoint properly protected');
        console.log(`   Status: ${error.response.status} - ${error.response.statusText}\n`);
      } else {
        console.log('❌ Unexpected error on create endpoint');
        console.log(`   Error: ${error.response?.status} - ${error.response?.statusText}\n`);
      }
    }

    // Test 4: Test pagination on public endpoint
    console.log('4. Testing GET /banners with pagination');
    try {
      const response = await axios.get(`${BASE_URL}/banners?limit=2&offset=0`);
      console.log('✅ Pagination works on public endpoint');
      console.log(`   Status: ${response.status}`);
      console.log(`   Returned: ${response.data.data?.rows?.length || 0} banners`);
      console.log(`   Total count: ${response.data.data?.count || 0}\n`);
    } catch (error) {
      console.log('❌ Pagination failed on public endpoint');
      console.log(`   Error: ${error.response?.status} - ${error.response?.statusText}\n`);
    }

    // Test 5: Login and test authenticated endpoints
    console.log('5. Testing authenticated endpoints...');
    const loginSuccess = await login();

    if (loginSuccess && authToken) {
      // Test admin endpoints with authentication
      console.log('6. Testing GET /banners/admin (with auth)');
      try {
        const response = await axios.get(`${BASE_URL}/banners/admin`, {
          headers: { Authorization: `Bearer ${authToken}` }
        });
        console.log('✅ Admin banners endpoint accessible with auth');
        console.log(`   Status: ${response.status}`);
        console.log(`   Total banners: ${response.data.data?.rows?.length || 0}\n`);
      } catch (error) {
        console.log('❌ Admin banners endpoint failed with auth');
        console.log(`   Error: ${error.response?.status} - ${error.response?.statusText}\n`);
      }

      // Test creating a banner with authentication
      console.log('7. Testing POST /banners/admin (with auth)');
      try {
        const response = await axios.post(`${BASE_URL}/banners/admin`, testBanner, {
          headers: { Authorization: `Bearer ${authToken}` }
        });
        console.log('✅ Banner created successfully');
        console.log(`   Status: ${response.status}`);
        console.log(`   Created banner ID: ${response.data.data?.id}\n`);
      } catch (error) {
        console.log('❌ Banner creation failed');
        console.log(`   Error: ${error.response?.status} - ${error.response?.statusText}`);
        console.log(`   Message: ${error.response?.data?.message || error.message}\n`);
      }
    }

    console.log('🎉 Banner endpoint tests completed!');
    console.log('\n📋 Summary:');
    console.log('- Public endpoint for active banners');
    console.log('- Admin endpoints properly protected');
    console.log('- Authentication system working');
    console.log('- Database integration functional');

  } catch (error) {
    console.error('❌ Test setup error:', error.message);
  }
}

// Run the test if server is available
testBannerEndpoints().catch(console.error);
