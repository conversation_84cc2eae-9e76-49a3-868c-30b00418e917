// Simple test script to verify banner functionality
const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function testBannerEndpoints() {
  console.log('Testing Banner Endpoints...\n');

  try {
    // Test 1: Get all active banners (public endpoint)
    console.log('1. Testing GET /banners (public endpoint)');
    try {
      const response = await axios.get(`${BASE_URL}/banners`);
      console.log('✅ Public banners endpoint accessible');
      console.log(`   Status: ${response.status}`);
      console.log(`   Response: ${JSON.stringify(response.data, null, 2)}\n`);
    } catch (error) {
      console.log('❌ Public banners endpoint failed');
      console.log(`   Error: ${error.response?.status} - ${error.response?.statusText}`);
      console.log(`   Message: ${error.response?.data?.message || error.message}\n`);
    }

    // Test 2: Try to access admin endpoint without authentication
    console.log('2. Testing GET /banners/admin (without auth)');
    try {
      const response = await axios.get(`${BASE_URL}/banners/admin`);
      console.log('❌ Admin endpoint should require authentication');
      console.log(`   Status: ${response.status}\n`);
    } catch (error) {
      if (error.response?.status === 401 || error.response?.status === 403) {
        console.log('✅ Admin endpoint properly protected');
        console.log(`   Status: ${error.response.status} - ${error.response.statusText}\n`);
      } else {
        console.log('❌ Unexpected error on admin endpoint');
        console.log(`   Error: ${error.response?.status} - ${error.response?.statusText}\n`);
      }
    }

    // Test 3: Try to create a banner without authentication
    console.log('3. Testing POST /banners/admin (without auth)');
    const testBanner = {
      title: 'Test Banner',
      description: 'Test Description',
      image_url: 'https://example.com/banner.jpg',
      link_url: 'https://example.com',
      start_date: new Date().toISOString(),
      end_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days from now
      position: 1,
      target_audience: 'ALL'
    };

    try {
      const response = await axios.post(`${BASE_URL}/banners/admin`, testBanner);
      console.log('❌ Create endpoint should require authentication');
      console.log(`   Status: ${response.status}\n`);
    } catch (error) {
      if (error.response?.status === 401 || error.response?.status === 403) {
        console.log('✅ Create endpoint properly protected');
        console.log(`   Status: ${error.response.status} - ${error.response.statusText}\n`);
      } else {
        console.log('❌ Unexpected error on create endpoint');
        console.log(`   Error: ${error.response?.status} - ${error.response?.statusText}\n`);
      }
    }

    console.log('Banner endpoint tests completed!');
    console.log('\nNote: To fully test the functionality, you would need:');
    console.log('- A running database with banner table');
    console.log('- Valid authentication tokens for admin endpoints');
    console.log('- Sample banner data in the database');

  } catch (error) {
    console.error('Test setup error:', error.message);
  }
}

// Run the test if server is available
testBannerEndpoints().catch(console.error);
