import { Router } from "express";
import { ProductRatingController } from "../../controllers/MarketPlace";
import { AuthMiddleware } from "../../middlewares";
import { <PERSON>rror<PERSON>and<PERSON> } from "../../utilities/errors";

const router = Router();

// Public routes (no authentication required)

// Get ratings for a specific product (public)
router.get(
  "/products/:productId/ratings",
  <PERSON>rrorHandler(ProductRatingController.getProductRatings)
);

// Get rating statistics for a product (public)
router.get(
  "/products/:productId/ratings/stats",
  Error<PERSON>andler(ProductRatingController.getProductRatingStats)
);

// Protected routes (authentication required)

// Create a new rating for a product
router.post(
  "/ratings",
  AuthMiddleware,
  ErrorHandler(ProductRatingController.create)
);

// Update an existing rating
router.put(
  "/ratings/:id",
  AuthMiddleware,
  ErrorHandler(ProductRatingController.update)
);

// Delete a rating
router.delete(
  "/ratings/:id",
  AuthMiddleware,
  Error<PERSON>andler(ProductRatingController.delete)
);

// Get a specific rating by ID
router.get(
  "/ratings/:id",
  ErrorHandler(ProductRatingController.getRatingById)
);

// Toggle helpfulness vote for a rating
router.post(
  "/ratings/:id/helpfulness",
  AuthMiddleware,
  ErrorHandler(ProductRatingController.toggleHelpfulness)
);

// Get current user's ratings
router.get(
  "/my-ratings",
  AuthMiddleware,
  ErrorHandler(ProductRatingController.getUserRatings)
);

export default router;
