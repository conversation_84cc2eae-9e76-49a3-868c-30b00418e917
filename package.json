{"name": "read_sea_api", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "ts-node src/app.ts", "dev": "nodemon"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@types/async": "^3.2.20", "@types/async-lock": "^1.4.2", "@types/bcrypt": "^5.0.1", "@types/compression": "^1.7.2", "@types/cors": "^2.8.13", "@types/express": "^4.17.21", "@types/helmet": "^4.0.0", "@types/jsonwebtoken": "^9.0.2", "@types/mime-types": "^2.1.1", "@types/mkdirp": "^2.0.0", "@types/morgan": "^1.9.4", "@types/multer": "^1.4.11", "@types/node": "^20.16.5", "@types/nodemailer": "^6.4.15", "@types/passport": "^1.0.16", "@types/passport-facebook-token": "^0.4.38", "@types/sequelize": "^4.28.15", "@types/swagger-jsdoc": "^6.0.1", "@types/swagger-ui-express": "^4.1.3", "@types/uuid": "^10.0.0", "concurrently": "^8.2.1", "nodemon": "^3.0.1", "sequelize-cli": "^6.6.3", "ts-node": "^10.9.1", "typescript": "^4.6.3"}, "dependencies": {"async": "^3.2.4", "async-lock": "^1.4.1", "axios": "^1.7.7", "bcrypt": "^5.1.1", "body-parser": "^1.20.2", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.21.0", "firebase-admin": "^12.5.0", "generic-pool": "^3.9.0", "helmet": "^7.0.0", "inversify": "^6.0.1", "ioredis": "^5.3.2", "joi": "^17.9.2", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "mysql2": "^3.3.3", "nodemailer": "^6.9.14", "passport": "^0.7.0", "passport-facebook-token": "^4.0.0", "pg": "^8.11.0", "pg-hstore": "^2.3.4", "pug": "^3.0.3", "qs": "^6.11.2", "sequelize": "^6.31.1", "sharp": "^0.33.5", "socket.io": "^4.7.2", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1"}}