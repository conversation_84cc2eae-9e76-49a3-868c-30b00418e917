import { Request, Response } from "express";
import { ProductRatingService } from "../../services/MarketPlace";
import { BadRequestError } from "../../errors/Errors";
import ServerResponse from "../../utilities/response/Response";


class ProductRatingController {
  // Create a new rating for a product
  static create = async (req: any, res: Response) => {
    try {
      const { product_id, rating, review_title, review_text } = req.body;
      const user = req.user;

      // Validation
      if (!product_id || !rating) {
        throw new BadRequestError("Product ID and rating are required");
      }

      if (rating < 1 || rating > 5 || !Number.isInteger(rating)) {
        throw new BadRequestError("Rating must be an integer between 1 and 5");
      }

      const result = await ProductRatingService.create(user, {
        product_id,
        rating,
        review_title,
        review_text
      });

      return SuccessResponse(res, result, "Rating created successfully");
    } catch (error: any) {
      throw error;
    }
  };

  // Update an existing rating
  static update = async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const { rating, review_title, review_text } = req.body;
      const user = req.user;

      // Validation
      if (rating !== undefined && (rating < 1 || rating > 5 || !Number.isInteger(rating))) {
        throw new BadRequestError("Rating must be an integer between 1 and 5");
      }

      const result = await ProductRatingService.update(user, id, {
        rating,
        review_title,
        review_text
      });

      return SuccessResponse(res, result, "Rating updated successfully");
    } catch (error: any) {
      throw error;
    }
  };

  // Delete a rating
  static delete = async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const user = req.user;

      const result = await ProductRatingService.delete(user, id);

      return SuccessResponse(res, { deleted: result }, "Rating deleted successfully");
    } catch (error: any) {
      throw error;
    }
  };

  // Get ratings for a specific product
  static getProductRatings = async (req: Request, res: Response) => {
    try {
      const { productId } = req.params;
      const {
        rating,
        verifiedPurchaseOnly,
        sortBy = 'newest',
        page = 1,
        limit = 10
      } = req.query;

      // Validation
      if (rating && (Number(rating) < 1 || Number(rating) > 5)) {
        throw new BadRequestError("Rating filter must be between 1 and 5");
      }

      const filters = {
        rating: rating ? Number(rating) : undefined,
        verifiedPurchaseOnly: verifiedPurchaseOnly === 'true',
        sortBy: sortBy as 'newest' | 'oldest' | 'helpful' | 'rating_high' | 'rating_low'
      };

      const pagination = {
        page: Number(page),
        limit: Math.min(Number(limit), 50) // Max 50 per page
      };

      const result = await ProductRatingService.getProductRatings(productId, filters, pagination);

      return SuccessResponse(res, result, "Product ratings retrieved successfully");
    } catch (error: any) {
      throw error;
    }
  };

  // Get rating statistics for a product (public endpoint)
  static getProductRatingStats = async (req: Request, res: Response) => {
    try {
      const { productId } = req.params;

      const result = await ProductRatingService.getProductRatings(productId, {}, { limit: 0 });

      return SuccessResponse(res, {
        stats: result.stats,
        totalRatings: result.total
      }, "Product rating statistics retrieved successfully");
    } catch (error: any) {
      throw error;
    }
  };

  // Toggle helpfulness vote for a rating
  static toggleHelpfulness = async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const { is_helpful } = req.body;
      const user = req.user;

      // Validation
      if (typeof is_helpful !== 'boolean') {
        throw new BadRequestError("is_helpful must be a boolean value");
      }

      const result = await ProductRatingService.toggleHelpfulness(user, id, is_helpful);

      return SuccessResponse(res, result, "Helpfulness vote updated successfully");
    } catch (error: any) {
      throw error;
    }
  };

  // Get user's own ratings
  static getUserRatings = async (req: Request, res: Response) => {
    try {
      const user = req.user;
      const { page = 1, limit = 10 } = req.query;

      const pagination = {
        page: Number(page),
        limit: Math.min(Number(limit), 50)
      };

      // This would need to be implemented in the service
      // For now, we'll return a placeholder response
      return SuccessResponse(res, {
        ratings: [],
        total: 0,
        message: "User ratings endpoint - to be implemented"
      }, "User ratings retrieved successfully");
    } catch (error: any) {
      throw error;
    }
  };

  // Get a specific rating by ID
  static getRatingById = async (req: Request, res: Response) => {
    try {
      const { id } = req.params;

      // This would need to be implemented in the service
      // For now, we'll return a placeholder response
      return SuccessResponse(res, {
        rating: null,
        message: "Get rating by ID endpoint - to be implemented"
      }, "Rating retrieved successfully");
    } catch (error: any) {
      throw error;
    }
  };
}

export default ProductRatingController;
