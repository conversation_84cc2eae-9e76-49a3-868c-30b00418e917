'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Create product_ratings table
    await queryInterface.createTable('product_ratings', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false,
      },
      user_id: {
        type: Sequelize.UUID,
        allowNull: false,
        comment: "User who submitted the rating",
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      product_id: {
        type: Sequelize.UUID,
        allowNull: false,
        comment: "Product being rated",
        references: {
          model: 'products',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      rating: {
        type: Sequelize.INTEGER,
        allowNull: false,
        validate: {
          min: 1,
          max: 5
        },
        comment: "Rating from 1 to 5 stars",
      },
      review_title: {
        type: Sequelize.STRING(255),
        allowNull: true,
        comment: "Optional title for the review",
      },
      review_text: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: "Optional detailed review text",
      },
      is_verified_purchase: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: "Whether this rating is from a verified purchase",
      },
      helpful_count: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: "Number of users who found this review helpful",
      },
      unhelpful_count: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: "Number of users who found this review unhelpful",
      },
      status: {
        type: Sequelize.ENUM('Active', 'Pending', 'Blocked', 'Archived'),
        allowNull: false,
        defaultValue: 'Active',
        comment: "Status of the rating/review",
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      deletedAt: {
        type: Sequelize.DATE,
        allowNull: true,
      },
    });

    // Create rating_helpfulness table
    await queryInterface.createTable('rating_helpfulness', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false,
      },
      user_id: {
        type: Sequelize.UUID,
        allowNull: false,
        comment: "User who marked the rating as helpful/unhelpful",
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      product_rating_id: {
        type: Sequelize.UUID,
        allowNull: false,
        comment: "Rating being marked as helpful/unhelpful",
        references: {
          model: 'product_ratings',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      is_helpful: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        comment: "true = helpful, false = unhelpful",
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      deletedAt: {
        type: Sequelize.DATE,
        allowNull: true,
      },
    });

    // Create indexes for product_ratings
    await queryInterface.addIndex('product_ratings', ['product_id']);
    await queryInterface.addIndex('product_ratings', ['user_id']);
    await queryInterface.addIndex('product_ratings', ['rating']);
    await queryInterface.addIndex('product_ratings', ['status']);
    await queryInterface.addIndex('product_ratings', ['is_verified_purchase']);
    await queryInterface.addIndex('product_ratings', ['product_id', 'status']);
    
    // Create unique constraint for one rating per user per product
    await queryInterface.addConstraint('product_ratings', {
      fields: ['user_id', 'product_id'],
      type: 'unique',
      name: 'unique_user_product_rating'
    });

    // Create indexes for rating_helpfulness
    await queryInterface.addIndex('rating_helpfulness', ['product_rating_id']);
    await queryInterface.addIndex('rating_helpfulness', ['user_id']);
    
    // Create unique constraint for one helpfulness vote per user per rating
    await queryInterface.addConstraint('rating_helpfulness', {
      fields: ['user_id', 'product_rating_id'],
      type: 'unique',
      name: 'unique_user_rating_helpfulness'
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('rating_helpfulness');
    await queryInterface.dropTable('product_ratings');
  }
};
